import axios from "./axios";
import { dataC } from "turing-plugin";

const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/app`;
const productUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/app-product`;
const areaBaseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/`;
// 获取业务应用列表
export function getAppList(data: any): Promise<any> {
  const params:any = {
    search: data.search || "",
    sort: data.sort || "createdDate,desc",
    page: data.page,
    size: data.size,
  };
  if (!dataC.isEmpty(data.type)) {
    params.type = data.type;
  } 
  return axios.get(baseUrl, { params });
}

// 新建业务应用
export function createApp(data: any): Promise<any> {
  const url = `${baseUrl}`;
  return axios.post(url, data);
}

// 编辑业务应用
export function editApp(data: any): Promise<any> {
  const url = `${baseUrl}/${data.id}`;
  return axios.put(url, data);
}

// 启用/停用
export function enableOrDisable(id: any, flag: boolean): Promise<any> {
  const url = `${baseUrl}/status/${id}?enabled=${flag}`;
  return axios.put(url);
}

// 查询业务应用详情
export function getAppDetail(id: any): Promise<any> {
  const url = `${baseUrl}/${id}`;
  return axios.get(url);
}

// 删除业务应用
export function deleteApp(id: any): Promise<any> {
  const url = `${baseUrl}/${id}`;
  return axios.delete(url);
}

// 获取应用业务列表
export function getTypes(): Promise<any> {
  const url = `${baseUrl}/types`;
  return axios.get(url);
}

// 获取启用的应用业务列表
export function getEnableList(): Promise<any> {
  const url = `${baseUrl}/enable`;
  return axios.get(url);
}

// 关联产品列表
export function getAppProductList(data: any): Promise<any> {
  const params = {
    sort: data.sort || "createdDate,desc",
  };
  const url = `${productUrl}/by-app/${data.id}`;
  return axios.get(url, { params });
}

// 产品启用/停用
export function enableOrDisableProduct(id: any, flag: boolean): Promise<any> {
  const url = `${productUrl}/status/${id}?enabled=${flag}`;
  return axios.put(url);
}

// 创建关联产品
export function createProduct(data: any): Promise<any> {
  const url = `${productUrl}`;
  return axios.post(url, data);
}

export function editProduct(data: any, id: any): Promise<any> {
  const url = `${productUrl}/${id}`;
  return axios.put(url, data);
}
// 删除关联产品
export function deleteProduct(id: any): Promise<any> {
  const url = `${productUrl}/${id}`;
  return axios.delete(url);
}

// 获取业务方
export function getAppPartnerList(): Promise<any> {
  const url = `${baseUrl}/names`;
  return axios.get(url);
}

// 关联产品列表
export function getRelateAppList(data: any): Promise<any> {
  const params = {
    sort: data.sort || "lastModifiedDate,desc",
  };
  const url = `${baseUrl}/by-product/${data.productId}`;
  return axios.get(url, { params });
}
export function getAreaEnum(): Promise<any> {
  const url = areaBaseUrl + 'region';
  return axios.get(url);
}
export function editEmail(id:any,emails:string): Promise<any> {
  const url = baseUrl + '/emails/'+id+`?emails=${emails}`;
  return axios.put(url);
}

export function getMcpJson(id: string): Promise<any> {
  const url = baseUrl + `/mcp/${id}`;
  return axios.put(url);
}

export function enabledMcpConfig(id: string, enabled: boolean): Promise<any> {
  const url = baseUrl + `/mcp/${id}/${enabled}`;
  return axios.put(url);
}

