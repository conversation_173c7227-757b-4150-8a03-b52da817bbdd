import axios from "./axios";
const oldBaseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1`;
const metaRegionBaseUrl = oldBaseUrl + "/meta/region";
const baseUrl = `/${import.meta.env.VITE_ASSET_NAME}/asset/api/v1`;
const bucketBaseUrl = baseUrl + "/bucket";
const bucketOnlineBaseUrl = baseUrl + "/bucket-online";
const taskBaseUrl = baseUrl + "/task";
const cellBaseUrl = baseUrl + "/cell";
const fieldItemBaseUrl = baseUrl + "/field-item";

/**
 * 获取区域
 */
export function getMetaRegionList(): Promise<any> {
  const url = metaRegionBaseUrl;
  return axios.get(url);
}

/**
 * 根据区域获取ES信息
 */
export function getRegionEsInfoList(region: string): Promise<any> {
  const url = oldBaseUrl + `/es/list?region=${region}`;
  return axios.get(url);
}

/**
 * 查询桶列表
 */
export function getBucketList(params: any): Promise<any> {
  const url = bucketBaseUrl;
  return axios.get(url, { params });
}

/**
 * 根据桶编码编码查询 数据单元的属性字段以及操作栏信息
 */
export function getCellSchema(bucketCode: string): Promise<any> {
  const url = bucketBaseUrl + `/schema-cell/${bucketCode}`;
  return axios.get(url);
}

/**
 * 保存
 */
export function saveBucketOnline(data: any): Promise<any> {
  const url = bucketOnlineBaseUrl;
  return axios.post(url, data);
}

/**
 * 查询列表
 */
export function getBucketOnlineListPage(params: any): Promise<any> {
  const url = bucketOnlineBaseUrl;
  return axios.get(url, { params });
}

/**
 * 查询详情
 */
export function getBucketOnlineDetail(id: string): Promise<any> {
  const url = bucketOnlineBaseUrl + `/${id}`;
  return axios.get(url);
}

/**
 * 同步
 */
export function syncBucketOnline(id: string): Promise<any> {
  const url = bucketOnlineBaseUrl + `/sync/${id}`;
  return axios.put(url);
}

/**
 * 启用
 */
export function enabledBucketOnline(id: string): Promise<any> {
  const url = bucketOnlineBaseUrl + `/enabled/${id}?enabled=true`;
  return axios.put(url);
}

/**
 * 启用
 */
export function disabledBucketOnline(id: string): Promise<any> {
  const url = bucketOnlineBaseUrl + `/enabled/${id}?enabled=false`;
  return axios.put(url);
}

/**
 * 删除
 */
export function deleteBucketOnline(id: string): Promise<any> {
  const url = bucketOnlineBaseUrl + `/${id}`;
  return axios.delete(url);
}

/**
 * 删除
 */
export function deleteBucketOnlineIdx(id: string): Promise<any> {
  const url = bucketOnlineBaseUrl + `/index-del/${id}`;
  return axios.delete(url);
}

/**
 * 导出
 */
export const exportBucketOnline = (): Promise<ArrayBuffer> => {
  const url = bucketOnlineBaseUrl + `/export`;
  return axios.get(url, { responseType: "blob" });
};

/**
 * 更新任务: 设置定时器
 */
export function saveBucketSchedule(bucketCode: string, cron: string, trigger: any): Promise<any> {
  const url = taskBaseUrl;
  return axios.post(url, { bucketCode, cron, trigger });
}

/**
 * 查询指定桶任务(进度)
 */
export function getBucketSchedule(bucketCode: string): Promise<any> {
  const url = taskBaseUrl + `/status/${bucketCode}`;
  return axios.get(url);
}

/**
 * 查询桶任务(进度)
 */
export function getTaskProcessList(bucketCodeList: Array<string>): Promise<any> {
  const url = taskBaseUrl + `/status-list`;
  return axios.get(url, { params: { bucketCodes: bucketCodeList.join(",") } });
}

/**
 * 查询桶任务执行记录
 */
export function getTaskExecutionListPage(params: any): Promise<any> {
  const url = taskBaseUrl + `/executions`;
  return axios.get(url, { params });
}

/**
 * 取消任务
 */
export function cancelTask(bucketCode: string, type: number): Promise<any> {
  const url = taskBaseUrl + `/${bucketCode}/cancel?type=${type}`;
  return axios.post(url);
}

/**
 * 强制完成任务
 */
export function forceFinishTask(bucketCode: string, type: number): Promise<any> {
  const url = taskBaseUrl + `/${bucketCode}/force-finish?type=${type}`;
  return axios.put(url);
}

/**
 * 失败重试
 */
export function failedRetryTask(bucketCode: string, executionId: string): Promise<any> {
  const url = taskBaseUrl + `/failed/${bucketCode}/retry/${executionId}`;
  return axios.put(url);
}

/**
 * 分页查询失败详情
 */
export function getFailDetailListPage(params: any): Promise<any> {
  const url = taskBaseUrl + `/failed/${params.bucketCode}/detail`;
  return axios.get(url, { params });
}

/**
 * 查询失败详情数量
 */
export function getFailDetailCount(params: any): Promise<any> {
  let url = taskBaseUrl + `/failed/${params.bucketCode}/count?executionId=${params.executionId}`;
  if (params.code != undefined) url = url + `&code=${params.code}`;
  return axios.get(url);
}

/**
 * 导出失败数据
 */
export const downloadFailDetail = (bucketCode: string, executionId: string, code: number): Promise<ArrayBuffer> => {
  let url = taskBaseUrl + `/failed/${bucketCode}/export?executionId=${executionId}`;
  if (code != undefined) url = url + `&code=${code}`;
  return axios.get(url, { responseType: "blob" });
};

/**
 * 预览数据单元
 */
export function getCellListPage(params: any): Promise<any> {
  const url = cellBaseUrl + `/list?page=${params.page}&size=${params.size}&sort=${params.sort}`;
  delete params.page;
  delete params.size;
  delete params.sort;
  return axios.post(url, params);
}

/**
 * 查询数据单元总量
 */
export function getCellCount(params: any): Promise<any> {
  const url = cellBaseUrl + `/count`;
  return axios.post(url, params);
}

/**
 * 查询数据单元详情
 */
export function getCellDetail(id: string, bucketCode: string, indexId: string, regionCode: string): Promise<any> {
  const url = cellBaseUrl + `/detail`;
  return axios.get(url, {
    params: { id, bucketCode, indexId, regionCode },
  });
}

/**
 * 修订数据单元
 */
export function corrcetCell(id: string, bucketCode: string, data: any): Promise<any> {
  const url = cellBaseUrl + `/correct/${bucketCode}/${id}`;
  return axios.post(url, data);
}

/**
 * 删除数据单元
 */
export function deleteCell(filter: any): Promise<any> {
  const url = cellBaseUrl + `/delete`;
  return axios.delete(url, { data: { filter } });
}

/**
 * 审核数据单元
 */
export function auditCell(filter: any, auditVal: number): Promise<any> {
  const url = cellBaseUrl + `/audit`;
  return axios.put(url, { filter, auditVal });
}

/**
 * 导出数据单元
 */
export function exportCell(filter: any): Promise<any> {
  const url = cellBaseUrl + `/export`;
  return axios.post(url, { filter }, { responseType: "blob" });
}

/**
 * 获取规格属性列表
 */
export function getFieldItemList(): Promise<any> {
  const url = fieldItemBaseUrl + `?enabled=true&sort=orderNum,asc`;
  return axios.get(url);
}