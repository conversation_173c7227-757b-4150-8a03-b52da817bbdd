
import axios from "./axios";

const baseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/mark-category/`;
const baseUrl1 = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/mark-stats/`;
const baseUrl3 = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/mark-standard`;

export function getStandardById(id: any): Promise<any> {
  const url = baseUrl3 + `/${id}`;
  return axios.get(url);
}

export function getStandardList() {
  const url = baseUrl3 + `/list`;
  return axios.get(url);
}

/**
 * 树
 */
export function createTree( data: any): Promise<any> {
  const url = baseUrl +'create';
  return axios.post(url, data);
}
export function createTree0( data: any): Promise<any> {
  const url = baseUrl +'createOneLevel';
  return axios.post(url, data);
}
export function createTree1( data: any): Promise<any> {
  const url = baseUrl +'createTwoLevel';
  return axios.post(url, data);
}

export function createTree2( data: any): Promise<any> {
  const url = baseUrl +'createThreeLevel';
  return axios.post(url, data);
}
export function getTree(params:any): Promise<any> {
  const url = baseUrl + "list";
  return axios.get(url,{params});
}
export function editTree(data: any): Promise<any> {
  const url = baseUrl +'update';
  return axios.put(url, data);
}
export function deleteTreeNode(id: any): Promise<any> {
  const url = baseUrl +id;
  return axios.delete(url);
}
// 表格
export function getTablePage(params:any): Promise<any> {
  const url = baseUrl1+ 'findByPage';
  return axios.get(url,{params});
}
// 表格导出
export function getTableExport(params?:any): Promise<any> {
  const url = baseUrl1+ 'export';
  return axios.get(url, { params: params,responseType: "blob" });
}

export function getAnalysisList(params:any): Promise<any> {
  const url = baseUrl1+ 'analysis';
  return axios.get(url,{params});
}

export function getFilterparams(id: any): Promise<any> {
  const url = baseUrl1+ 'filter-params/'+id;
  return axios.get(url);
}
export function exportTopK(id: any,params:any): Promise<any> {
  const url = baseUrl1+ 'analysis/export';
  return axios.get(url, { params:{categoryId:id,...params},responseType: "blob" });
}