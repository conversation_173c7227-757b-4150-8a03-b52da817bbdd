import axios from "./axios";
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta`;
// const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/meta/api/v1`;
const metaBaseUrl = baseUrl + "/node/";
const areaBaseUrl = baseUrl + "/";
// const  areaBaseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/`

const dicBaseUrl = baseUrl + "/word/";
const wordBaseUrl = baseUrl + "/dict/";
/**
 * 所属区域
 */
export function getAreaEnum(): Promise<any> {
  const url = areaBaseUrl + 'region';
  return axios.get(url);
}

/**
 * 树
 */
export function createTree(type: any, data: any,area:any): Promise<any> {
  const url = metaBaseUrl +area+type;
  return axios.post(url, data);
}

export function editTree(id: any, data: any,area:any): Promise<any> {
  const url = metaBaseUrl +area+ id;
  return axios.put(url, data);
}
export function deleteTreeNode(id: any,area:any): Promise<any> {
  const url = metaBaseUrl +area+ id;
  return axios.delete(url);
}
export function getTree(type: string, params: any,area:any): Promise<any> {
  const url = metaBaseUrl + "list/" +area+ type;
  return axios.get(url, {params});
}
export function sortTree(data: any,area:any): Promise<any> {
  const url = metaBaseUrl +area+ "/sort";
  return axios.put(url, data);
}
export function syncTree(id: string, targetRegion: any,area:any,region:any): Promise<any> {
  const url = baseUrl+ '/'+region+ '/'+area + `sync/${id}?targetRegion=${targetRegion}`;;
  return axios.post(url);
}
/**
 * 词典
 */
export function getDicPage(id: any, params: any,area:any): Promise<any> {
  const url = dicBaseUrl+area + id;
  return axios.get(url, { params });
}
export function addDic(data: any,area:any): Promise<any> {
  const url = dicBaseUrl+area;
  return axios.post(url, data);
}
export function deleteDic(id: any,area:any): Promise<any> {
  const url = dicBaseUrl +area+ id;
  return axios.delete(url);
}
export function editDic(id: any, data: any,area:any): Promise<any> {
  const url = dicBaseUrl +area+ id;
  return axios.put(url, data);
}
export function exportDic(params: any,area:any): Promise<any> {
  const url = dicBaseUrl +area+ "/exportWords";
  return axios.get(url, { params, responseType: "blob" });
}
export function importDic(data: any,area:any): Promise<any> {
  const url = dicBaseUrl+area + "/importWords";
  return axios.post(url, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
/**
 * 字典典
 */
export function getWordPage(id: any, params: any,area:any): Promise<any> {
  const url = wordBaseUrl+area + id;
  return axios.get(url, { params });
}
export function getWordAll(id: any, params: any,area:any): Promise<any> {
  const url = wordBaseUrl+'list/'+area + id;
  return axios.get(url, { params });
}
export function addWord(data: any,area:any): Promise<any> {
  const url = wordBaseUrl+area;
  return axios.post(url, data);
}
export function deleteWord(id: any,area:any): Promise<any> {
  const url = wordBaseUrl +area+ id;
  return axios.delete(url);
}
export function editWord(id: any, data: any,area:any): Promise<any> {
  const url = wordBaseUrl +area+ id;
  return axios.put(url, data);
}

export function exportWord(params: any,area:any, type: string): Promise<any> {
  const url = wordBaseUrl+area + `/exportDict/${type}`;
  return axios.get(url, { params, responseType: "blob" });
}
export function importWord(data: any,area:any, type: string): Promise<any> {
  const url = wordBaseUrl +area+ `/importDict/${type}`;
  return axios.post(url, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
export function sortWord(data: any,area:any): Promise<any> {
  const url = wordBaseUrl +area+ "/sort";
  return axios.put(url, data);
}

export function getMetaDictList(dict: string): Promise<any> {
  const url = wordBaseUrl + `${dict}/list`;
  return axios.get(url);
}
