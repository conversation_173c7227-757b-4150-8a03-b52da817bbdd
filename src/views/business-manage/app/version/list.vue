<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="compVersion-list height-adaptive" style="padding: 10px">
      <el-card class="info-card">
        <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
              <span>基本信息</span>&nbsp;
              <!-- <el-button link type="primary" @click.native.stop="events.editComp(appDetail)" :disabled="appDetail.enabled">
                <el-icon size="18"><Edit /></el-icon>
              </el-button> -->
              <my-button
                link
                type="primary"
                @click="events.editComp(appDetail)"
                :stopPropagation="true"
                operationAuth="/base/#/app/edit"
                :disabled="appDetail.enabled"
                disabledTips="已启用，不可编辑"
                ><el-icon size="18"><Edit /></el-icon
              ></my-button>
            </template>
            <el-descriptions :column="2">
              <el-descriptions-item label-class-name="bold" label="AppId：">
                {{ getText(appDetail.appId) }}
                <el-link class="icon-copy" type="primary" :underline="false" @click="copyText(appDetail.appId)" :icon="CopyDocument" />
              </el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="AppSecret: ">
                {{ events.maskAppSecret(getText(appDetail.appSecret)) }}
                <el-link class="icon-copy" type="primary" :underline="false" @click="copyText(appDetail.appSecret)" :icon="CopyDocument" />
              </el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="创建时间：">{{
                timeC.format(appDetail.createdDate, "YYYY-MM-DD hh:mm:ss")
              }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="创建人：">{{ getText(appDetail.createdBy) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="描述：">{{ getText(appDetail.description) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="MCP：">
                <el-switch v-model="appDetail.mcp" @change="events.handleMcpChange" :active-value="true" :inactive-value="false" />
                <el-popover v-if="appDetail.mcp" placement="right" width="auto" trigger="click" @before-enter="events.handleMcpCopy">
                  <template #reference>
                    <el-button link type="primary" :icon="CopyDocument" style="margin-left: 10px" />
                  </template>
                  <div class="json-viewer-container">
                    <json-viewer :value="appDetail.mcpJson" expanded :expand-depth="5" sort></json-viewer>
                  </div>
                </el-popover>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <el-card class="table-card" style="flex: 1">
        <div style="display: flex">
          <el-descriptions>
            <template #title>
              <span>关联产品</span>&nbsp;
              <el-button link type="primary" @click="events.searchQuery">
                <el-icon size="18"><Refresh /></el-icon>
              </el-button>
            </template>
          </el-descriptions>
          <my-button type="primary" @click="events.add" :icon="CirclePlus" style="margin-left: auto" operationAuth="/base/#/app/edit">关联产品</my-button>
        </div>
        <div style="height: calc(100% - 40px)">
          <table-page
            ref="myTableRef"
            operationAuth="/base/#/app/edit"
            :query="query"
            :columns="columns"
            :operations="operations"
            :loadDataApi="loadListData"
            :transformQuery="transformQuery"
            :transformListData="transformListData"
            @operation="handleOperation"
          >
            <template #description="scope">
              <div class="flex">
                <div class="btn-title">
                  <span v-if="appDetail.mcp && !scope.row.description">
                    <el-icon color="#F3960C"><Warning /></el-icon>
                    <el-text type="warning">描述为空影响mcp接入LLM效果</el-text>
                  </span>
                  <span v-else>{{ scope.row.description }}</span>
                </div>
                <el-button link type="primary" :icon="Edit" @click="events.edit(scope.row)"> </el-button>
              </div>
            </template>
          </table-page>
        </div>
      </el-card>
    </div>
  </page-wrapper>
  <AddDialog ref="addVersionRef" @reload="getDetail" />
  <AddProductDialog ref="addProductRef" @reload="loadList" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { useI18n } from "vue-i18n";
import { copyText, getText } from "@/utils/helpers";
import { deleteProduct, enableOrDisableProduct, getAppDetail, getAppProductList, getMcpJson, enabledMcpConfig } from "@/api/app.ts";
import { getProductList } from "@/api/product.ts";
import useCtx from "@/hooks/useCtx";
import { CirclePlus, CopyDocument, Edit } from "@element-plus/icons-vue";
import { timeC } from "turing-plugin/lib";
import AddDialog from "../add.vue";
import AddProductDialog from "./add.vue";
import { unitMap } from "./config.ts";
import JsonViewer from "vue-json-viewer";

const { t } = useI18n();
const { $app, proxy, $router, $route } = useCtx();
const $id = $route.params.id as string;
const routeName = "app::version";

const appDetail = ref<any>({});
const getDetail = async () => {
  const res = await getAppDetail($id);
  appDetail.value = res || {};
  if (appDetail.value.mcp) {
    appDetail.value.mcpJson = (await getMcpJson($id)).data;
  }
};
const activeCollapse = ref([1]);
// 获取产品列表
const productList = ref({});
const getProductListApi = async () => {
  getProductList({ size: 100 }).then((res) => {
    if (res?.content?.length) {
      res?.content?.forEach((x: any) => {
        productList.value[x.id] = {
          name: x.name,
          value: x.id,
          code: x.code,
        };
      });
    }
  });
};
getProductListApi();

/* 查询 */
const query = ref<any>({});

/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  { prop: "productName", label: "产品名称", sortable: false, minWidth: 180 },
  { prop: "description", label: "描述", slotName: "description", minWidth: 280 },
  { prop: "productCode", label: "产品编码", sortable: false, minWidth: 180 },
  { prop: "metaRegionCode", label: "环境配置", sortable: false, width: 100 },
  { prop: "concurrentQuota", label: "并发配额", width: 120 },
  { prop: "qpsLimitRender", label: "QPS使用限制", width: 140 },
  { prop: "requestLimitRender", label: "调用量限制", width: 140 },
  {
    prop: "enabled",
    label: "是否启用",
    width: 120,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        $app.$confirm({ title: `您确认要${record.enabled ? "停用" : "启用"}关联产品“${record.productName}”吗？` }).then((res) => {
          enableOrDisableProduct(record.id, !record.enabled).then((res) => {
            loadList();
            $app.$message.success(record.enabled ? "关联产品停用成功" : "关联产品启用成功");
          });
        });
      },
    },
  },
  { prop: "authDeadlineRender", label: "授权截止时间", width: 200 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
]);
const operations = [
  { type: "edit", label: t("btn.edit") },
  {
    type: "delete",
    label: t("btn.delete"),
    btnType: "danger",
    disabled: (record: any) => record.relates,
    disabledTips: "存在已关联产品的版本禁止删除",
  },
  // { type: "view", label: '查看协议'}
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};

//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    getAppProductList(data).then((result) => {
      resolve({
        content: result.data,
        totalElements: result.data.length,
      });
    });
  });
};

// 转换传参
const transformQuery = ({ ...rest }) => {
  const query = {
    id: $id,
    ...rest,
  };
  return query;
};

// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.qpsLimitRender = x.qpsLimit ? `${x.qpsLimit}${unitMap[x.qpsUnit]?.label}` : "--";
    x.requestLimitRender = x.requestLimit == -1 ? "无限量" : x.requestLimit;
    x.authDeadlineRender = (x.authDeadline && timeC.format(x.authDeadline, "YYYY-MM-DD HH:mm:ss")) || "-";
    return x;
  });
};

/* events */
const events = reactive<any>({
  handleMcpChange: async (newVal: boolean, oldVal: boolean) => {
    try {
      await enabledMcpConfig($id, newVal);
      if (appDetail.value.mcp) {
        appDetail.value.mcpJson = (await getMcpJson($id)).data;
      }
    } catch (error) {
      appDetail.value.mcp = oldVal;
    }
  },
  handleMcpCopy: () => {
    copyText(JSON.stringify(appDetail.value.mcpJson));
  },
  collapseChange: (val: string[]) => {
    setTimeout(() => {
      proxy.$refs.myTableRef.mediaHeight();
    }, 500);
  },
  searchQuery: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  edit: (record: any) => {
    proxy.$refs.addProductRef?.openDialog("edit", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { t: "关联产品", n: record.name }),
      })
      .then(() => {
        deleteProduct(record.id).then((res: any) => {
          $app.$message.success("关联产品删除成功！");
          loadList();
        });
      });
  },
  add: () => {
    proxy.$refs.addProductRef?.openDialog("add", $id);
  },
  view: (record: any) => {},
  editComp: (record: any) => {
    proxy.$refs.addVersionRef?.openDialog("edit", record);
  },
  maskAppSecret: (appSecret: any) => {
    // 假设我们隐藏中间6位字符
    if (!appSecret) {
      return "";
    }
    const start = appSecret.substring(0, 3); // 显示前3位
    const end = appSecret.substring(appSecret.length - 3); // 显示后3位
    const masked = start + "******" + end; // 中间6位用星号替换
    return masked;
  },
});

/* 列表刷新 */
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};

onMounted(() => {
  getDetail();
});
</script>

<style lang="scss" scoped>
.compVersion-list {
  padding: 10px;

  .el-card {
    :deep(.el-card__body) {
      height: 100%;
    }
  }

  .info-card {
    height: auto;
    margin-bottom: 10px;

    :deep(.el-descriptions__label.bold) {
      font-weight: bold;
      background: #fff !important;
    }
  }

  :deep(.query-wrapper),
  :deep(.table-wrapper) {
    padding: 0;
  }
}
.flex {
  width: 100%;
  .btn-title {
    width: calc(100% - 30px);
    text-align: left;
    margin: 0 5px;

    > span {
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .btn-title.el-button + .el-button {
    margin-left: 0;
  }
}

::v-deep .jv-code.open {
  padding: 10px !important;
}
</style>
