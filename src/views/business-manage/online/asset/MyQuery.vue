<!-- 数据预览定制查询组件 -->
<template>
  <div class="query-page-wrapper">
    <el-form style="width: 100%" @submit.native.prevent inline>
      <el-form-item
        class="query-item"
        v-for="(item, key) in actualQueryItems"
        :key="key"
        :label="!item.labelHidden ? item.label : ''"
        :label-width="item.labelWidth"
        v-show="!item.hidden && item.checked"
      >
        <!-- 输入框 -->
        <el-input
          v-if="item.style === 'input'"
          v-model="item.modelValue"
          :style="{ width: item.width || '180px' }"
          v-bind="{
            placeholder: `${item.label}`,
            clearable: true,
            ...item.attrs,
          }"
          v-on="item.events"
          @keydown.enter="search"
        >
        </el-input>
        <!-- 数字输入框 -->
        <el-input
          v-else-if="item.style === 'number'"
          v-model="item.modelValue"
          type="number"
          :style="{ width: item.width || '180px' }"
          v-bind="{
            placeholder: `${item.label}`,
            clearable: true,
            ...item.attrs,
          }"
          v-on="item.events"
          @keydown.enter="search"
        >
          <template #prepend>
            <el-select v-model="item.operateValue" style="width: 75px" placeholder="操作">
              <el-option label="等于" value="EQ" />
              <el-option label="小于" value="LT" />
              <el-option label="小于等于" value="LE" />
              <el-option label="大于" value="GT" />
              <el-option label="大于等于" value="GE" />
            </el-select>
          </template>
        </el-input>
        <!-- 选择框 -->
        <el-select
          v-else-if="item.style === 'select'"
          v-model="item.modelValue"
          :style="{ width: item.width || '180px' }"
          v-bind="{
            placeholder: `${item.label}`,
            clearable: true,
            filterable: true,
            'collapse-tags': true,
            'collapse-tags-tooltip': true,
            'max-collapse-tags': 1,
            ...item.attrs,
          }"
          v-on="item.events"
          @change="search"
        >
          <el-option v-for="sub in item.options" :key="sub.value" :label="sub.label" :value="sub.value"> </el-option>
        </el-select>
        <!-- 日期(时间)选择范围 -->
        <el-date-picker
          v-else-if="item.style === 'datetime'"
          v-model="item.modelValue"
          type="datetimerange"
          :style="{ width: item.width || '372px' }"
          v-bind="{
            'range-separator': '至',
            'start-placeholder': `${item.label}开始`,
            'end-placeholder': `${item.label}结束`,
            format: datetimeFormat,
            'value-format': datetimeFormat,
            'popper-class': 'custom-content-view',
            ...item.attrs,
          }"
          v-on="item.events"
          @change="search"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item class="query-item" v-for="(item, key) in extraQueryItems" v-show="item.checked" :key="key" label="" :label-width="0">
        <el-select v-model="item.keyValue" style="width: 136px" :placeholder="item.label || '字段名'">
          <template v-if="!dataC.isEmpty(item.options)">
            <el-option v-for="item1 in item.options" :label="`${item1.label}(${item1.value})`" :value="item1.value" />
          </template>
          <template v-if="dataC.isEmpty(item.options)">
            <el-option v-for="item1 in extraQueryKeys" :label="`${item1.label}(${item1.value})`" :value="item1.value" />
          </template>
        </el-select>
        <el-select v-model="item.operateValue" style="width: 100px" placeholder="操作">
          <el-option label="等于" value="EQ" />
          <el-option label="不等于" value="NE" />
          <el-option label="包含" value="CONTAINS" />
          <el-option label="不包含" value="NOT_CONTAINS" />
          <el-option label="包含正则" value="REGEX" />
          <el-option label="不包含正则" value="NOT_REGEX" />
        </el-select>
        <el-select v-if="!dataC.isEmpty(item.valueOptions)" v-model="item.modelValue" style="width: 136px" placeholder="字段值" allow-create @change="search">
          <el-option v-for="item1 in item.valueOptions" :label="item1.label" :value="item1.value" />
        </el-select>
        <el-input
          v-if="dataC.isEmpty(item.valueOptions)"
          v-model="item.modelValue"
          style="width: 136px"
          placeholder="字段值"
          clearable
          @keydown.enter="search"
        />
      </el-form-item>
      <!-- 刷新按钮 -->
      <el-form-item style="margin-right: 10px">
        <el-button class="operate-btn" :icon="Refresh" @click="reset" />
      </el-form-item>
      <!-- 设置按钮 -->
      <el-form-item style="margin-right: 10px">
        <el-popover placement="right" :width="150" trigger="click">
          <template #reference>
            <el-button class="operate-btn" :icon="Setting"> </el-button>
          </template>
          <template v-for="(item, key) in actualQueryItems">
            <div v-show="!item.hidden" style="height: 25px">
              <el-checkbox v-model="item.checked" :value="true" @change="queryItemsCheckedChange(key, item.checked)">
                {{ item.label || item.attrs?.placeholder || "" }}
              </el-checkbox>
            </div>
          </template>
          <template v-for="(item, index) in extraQueryItems">
            <div style="height: 25px; display: flex; justify-content: space-between; align-items: center">
              <el-checkbox v-model="item.checked" :value="true">
                {{ item.label || item.keyValue || "暂未选择" }}
              </el-checkbox>
              <el-button
                v-if="dataC.isEmpty(item.label)"
                type="danger"
                circle
                :icon="Minus"
                @click="removeExtraQueryItems(index)"
                size="small"
                style="--el-button-size: 16px"
              />
            </div>
          </template>
        </el-popover>
      </el-form-item>
      <!-- 新增按钮 -->
      <el-form-item style="margin-right: 0">
        <el-button class="operate-btn" :icon="Plus" @click="insertExtraQueryItems" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="TQuery">
import { ref, watch, computed, onMounted, nextTick } from "vue";
import { keys, cloneDeep, debounce } from "lodash";
import { Refresh, Search, Setting, Plus, Minus } from "@element-plus/icons-vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
const { $app } = useCtx();

const props = defineProps({
  queryItems: { type: Object, default: () => {} },
  name: { type: String, default: "default" },
});
const emits = defineEmits(["search", "reset"]);

const datetimeFormat = ref<string>("YYYY-MM-DD HH:mm:ss");

//查询项的key
const queryKey = computed(() => `${$app.$route.path}:query:${props.name}`);
//查询项的实际展示
const actualQueryItems = ref({});
//查询项的扩展展示
const extraQueryItems = ref([]);
const extraQueryKeys = computed(() => {
  const res = [];
  for (const [key, item] of Object.entries(actualQueryItems.value)) {
    res.push({ label: item.label, value: key });
  }
  return res;
});
//动态查询项
// 备份一份，方便重置
const queryItemsCopy = ref({});
watch(
  () => props.queryItems,
  (newQueryItems) => {
    queryItemsCopy.value = cloneDeep(newQueryItems);
    const oldActualQueryItems = cloneDeep(actualQueryItems.value);
    const clonedItems = cloneDeep(newQueryItems);
    const storageKey = queryKey.value;
    const storedData = dataC.safeObject(localStorage.getItem(storageKey)) || {};
    const updatedStorage = { ...storedData };

    actualQueryItems.value = {};
    extraQueryItems.value = [];
    Object.entries(clonedItems).forEach(([key, item]) => {
      if (!(key in updatedStorage)) {
        updatedStorage[key] = { checked: true };
      }

      item.checked = updatedStorage[key].checked;
      item.modelValue = dataC.isEmpty(item.modelValue) ? oldActualQueryItems[key]?.modelValue : item.modelValue;
      item.defaultValue = item.defaultValue ?? item.modelValue;

      if (!(item.fieldType === "OBJECT" && item.style === "select")) {
        actualQueryItems.value[key] = item;
      } else {
        extraQueryItems.value.push({
          checked: true,
          label: item.label,
          keyValue: "",
          operateValue: "EQ",
          modelValue: "",
          defaulValue: "",
          options: item.options,
          valueOptions: key === "levels" ? item.valueOptions.map((item) => ({ label: item.label, value: parseInt(item.value) })) : item.valueOptions,
        });
      }
    });

    localStorage.setItem(storageKey, JSON.stringify(updatedStorage));
  },
  { immediate: true, deep: true }
);
const queryItemsCheckedChange = (key: String, checked: Boolean) => {
  //获取缓存内容
  const storage = dataC.safeObject(localStorage.getItem(queryKey.value)) || {};
  //记录查询项选中情况
  storage[key] = { checked };
  localStorage.setItem(queryKey.value, JSON.stringify(storage));
};

// 查询条件赋值
const getQuery = (obj: any) => {
  let query: any = {};
  for (let key in obj) {
    query[key] = {
      modelValue: obj[key].modelValue,
      operateValue: obj[key].operateValue,
    };
  }
  query.extraQuery = cloneDeep(
    extraQueryItems.value.filter(
      (extraItem) => !dataC.isEmpty(extraItem.keyValue) && !dataC.isEmpty(extraItem.operateValue) && !dataC.isEmpty(extraItem.modelValue)
    )
  );
  return query;
};
const toSearch = (arr: any) => {
  const query = getQuery(arr); // 获取查询条件的值
  emits("search", query); // 查询
};
const search = () => {
  toSearch(actualQueryItems.value);
};
const reset = () => {
  emits("reset", queryItemsCopy.value); // 清空查询条件
  const queryItems = actualQueryItems.value;

  Object.keys(queryItems).forEach((key) => {
    const item = queryItems[key];
    if (item) {
      item.modelValue = item.defaultValue;
    }
  });
  extraQueryItems.value.forEach((extraItem) => (extraItem.modelValue = extraItem.defaultValue));

  toSearch(queryItems);
};
const insertExtraQueryItems = () => {
  extraQueryItems.value.push({
    checked: true,
    keyValue: "",
    operateValue: "EQ",
    modelValue: "",
    defaultValue: "",
  });
};
const removeExtraQueryItems = (index: number) => {
  extraQueryItems.value.splice(index, 1);
};

onMounted(() => {});
defineExpose({ search, reset });
</script>

<style lang="scss" scoped>
.query-page-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
  .query-item {
    margin-right: 0 !important;
    padding-right: 12px;
  }
  .operate-btn {
    padding: 8px;
  }
}
</style>
