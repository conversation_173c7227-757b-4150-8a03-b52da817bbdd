<!-- 数据预览定制逻辑表格 -->
<template>
  <div class="table-page-wrapper height-adaptive" ref="table-page-wrapper">
    <!-- 查询+操作都写在这里 -->
    <div class="query-wrapper" ref="custom-query">
      <slot name="query"></slot>
    </div>
    <!-- 表格 -->
    <div class="table-wrapper" style="height: 100%">
      <el-table
        ref="myTableRef"
        class="custom-table"
        :data="viewTableData"
        :height="table.tableHeight"
        v-bind="{
          border: true,
          'show-overflow-tooltip': {
            placement: 'left',
          },
          ...$attrs,
        }"
        @sort-change="events.handleSortChange"
        :row-key="rowKey"
        :row-class-name="rowClassName"
      >
        <!-- 多选 -->
        <el-table-column v-if="withSelect" width="70" label="" fixed="left">
          <template #header>
            <el-checkbox v-model="selectCur" :value="true" />
            <el-dropdown trigger="click">
              <el-button link :icon="ArrowDownBold"> </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="events.selectAll">
                    <div>
                      <el-icon v-if="selectAll"><Select /></el-icon>
                      <span :style="{ 'margin-left': selectAll ? '0' : '19px' }">选择全部</span>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item @click="events.selectCur">
                    <div>
                      <el-icon v-if="!selectAll && selectCur"><Select /></el-icon>
                      <span :style="{ 'margin-left': !selectAll && selectCur ? '0' : '19px' }">选择当前页</span>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item @click="events.clearAll"><span style="margin-left: 19px">清除选择</span></el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template #default="scope"><el-checkbox :value="true" v-model="scope.row.selected" :disabled="scope.row.disabled" /></template>
        </el-table-column>
        <!-- 序号 -->
        <el-table-column type="index" width="80" label="序号" fixed="left" v-if="withOrder">
          <template #header>
            <div class="flex">
              <span>序号&nbsp;</span>
              <el-popover placement="right" :width="150" trigger="click" popper-class="column-popover">
                <template #reference>
                  <el-button link :icon="Operation"> </el-button>
                </template>
                <el-button link :icon="Refresh" type="info" @click="resetActualColumns">默认配置</el-button>
                <el-divider border-style="dashed" style="margin: 2px 0" />
                <div :id="`column-checkbox-list-${name}`">
                  <div v-for="item in actualColumns" :key="`col-${item.prop}-${item.order}`">
                    <el-checkbox
                      v-if="item.prop != 'operation'"
                      v-model="item.checked"
                      :value="true"
                      @change="events.handleColumnChange(item.prop, item.checked)"
                    >
                      {{ item.label }}
                    </el-checkbox>
                  </div>
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="{ $index }">
            <span v-if="page">{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
            <span v-else>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <template v-for="(item, index) of actualColumns">
          <el-table-column
            :key="index"
            resizable
            v-bind="{
              sortable: withSort == false || item.sortable == false || item.prop === 'operation' ? false : 'custom',
              ...item,
            }"
            v-if="item.checked"
          >
            <!-- 默认项 -->
            <template #default="scope" v-if="!item.custom">
              {{ getColumnText(scope.row, item.prop) }}
              <el-tag size="small" style="margin-left: 6px" v-if="item.withTag && item.tagProps && item.tagProps.show(scope.row)">
                {{ item.tagProps.label }}
              </el-tag>
            </template>
            <!-- 支持复制的项 -->
            <template #default="scope" v-if="!item.custom && item.withCopy">
              <div class="flex">
                <div class="btn-title">
                  <span>{{ getColumnText(scope.row, item.prop) }}</span>
                </div>
                <el-icon class="icon-copy" @click="copyText(scope.row[item.prop])">
                  <CopyDocument />
                </el-icon>
              </div>
            </template>
            <!-- 可点击的项 -->
            <template #default="scope" v-if="item.custom === 'link' && item.customRender">
              <text-button
                v-if="!isEmpty(scope.row[item.prop])"
                type="primary"
                @click="item.customRender.click(scope.row)"
                :disabled="item.customRender.disabled && item.customRender.disabled(scope.row)"
                :style="{ fontWeight: item.blod ? 550 : 400 }"
              >
                {{ scope.row[item.prop] }}
              </text-button>
              <span v-else>{{ NO_TEXT }}</span>
            </template>
            <!-- 状态项 -->
            <template #default="scope" v-if="['status', 'tagStatus'].includes(item.custom) && item.customRender">
              <template v-if="!isEmpty(scope.row[item.prop])" v-for="(obj, key) in item.customRender.options" :key="key">
                <component
                  :is="item.custom === 'status' ? 'status-dot' : 'status-tag'"
                  :type="obj.type"
                  :name="obj.name || obj.label"
                  :reason="item.customRender.reason ? item.customRender.reason(scope.row) : null"
                  v-if="
                    (!Array.isArray(item.customRender.options) && scope.row[item.prop] == key) ||
                    (Array.isArray(item.customRender.options) && scope.row[item.prop] == obj.value)
                  "
                />
              </template>
              <span v-else>{{ NO_TEXT }}</span>
            </template>
            <!-- 其他自定义组件 按需添加 -->
            <!-- 插槽 -->
            <template #default="scope" v-if="item.slotName">
              <slot :name="item.slotName" :row="scope.row"></slot>
            </template>
            <!-- 操作项 -->
            <template #default="scope" v-if="item.prop === 'operation' && !item?.slotName">
              <div class="flex">
                <!-- 在外面的操作按钮 -->
                <template v-for="(operation, i) of noCollapsedOperations">
                  <!-- 禁用的按钮需要给出禁用提示 -->
                  <my-button
                    link
                    :type="operation.btnType || 'primary'"
                    :key="'operation' + i"
                    v-if="isExit(scope.row, operation)"
                    :disabledTips="getDisabledTips(scope.row, operation)"
                    :disabled="isDisabled(scope.row, operation)"
                    @click="events.handleOperation(scope.row, operation, scope.$index)"
                  >
                    {{ operation.label }}
                  </my-button>
                </template>
                <!-- 在更多里面的操作按钮 -->
                <el-dropdown v-if="collapsedOperations.length > 0">
                  <my-button link type="primary" style="margin-left: 12px">
                    更多<el-icon style="margin-left: 5px"><arrow-down /></el-icon>
                  </my-button>
                  <template #dropdown>
                    <template v-for="(operation, i) in collapsedOperations">
                      <el-dropdown-item :key="'operation2' + i" v-if="isExit(scope.row, operation)">
                        <my-button
                          link
                          :type="operation.btnType || 'primary'"
                          :disabled="isDisabled(scope.row, operation)"
                          :disabledTips="getDisabledTips(scope.row, operation)"
                          @click="events.handleOperation(scope.row, operation, scope.$index)"
                        >
                          {{ operation.label }}
                        </my-button>
                      </el-dropdown-item>
                    </template>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </template>
        <template #empty>
          <my-empty :size="120" />
        </template>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pagination-wrapper" ref="custom-pagination" v-if="withPagination">
      <slot name="pagination"></slot>
      <my-pagination v-bind="page" @current-change="events.currentChange" @size-change="events.sizeChange" pageLayout="prev, pager, next, sizes" />
      <el-button type="primary" @click="events.continueLoad" style="margin-left: 15px">继续读取</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, onUnmounted, nextTick, watch, ref, computed } from "vue";
import { assign, cloneDeep, debounce, _ } from "lodash";
import { textC, dataC } from "turing-plugin";
import { copyText, getText } from "@/utils/helpers";
import { NO_TEXT } from "@/utils/constants";
import useCtx from "@/hooks/useCtx";
import TextButton from "@/components/button/TextButton.vue";
import { CopyDocument, ArrowDownBold, Operation, Refresh } from "@element-plus/icons-vue";
import { useSlots } from "vue";
import Sortable from "sortablejs";
const slots = useSlots();
const { isEmpty } = dataC;
const { proxy, $app, $auth } = useCtx();
const testAuth = () => {
  if (!dataC.isEmpty(props.operationAuth) && !$auth.testAuth(props.operationAuth)) {
    return false;
  }
  return true;
};
const props = defineProps({
  rowKey: { type: String, default: "id" },
  // 查询栏相关
  query: {
    default() {
      return {};
    },
  },
  // 列表相关
  columns: {
    type: Array<any>,
    default() {
      return [];
    },
  },
  //排序相关
  withSort: { type: Boolean, default: true },
  withPagination: { type: Boolean, default: true },
  operations: {
    type: Array<any>,
    default() {
      return [];
    },
  },
  operationAuth: { type: String, default: "" },
  //排序相关
  withSort: { type: Boolean, default: true },
  //多选相关
  withSelect: { type: Boolean, default: false },
  //序号相关
  withOrder: { type: Boolean, default: true },
  // 列表查询接口
  loadDataApi: { type: Function, default: (e: any) => e },
  // 处理请求参数
  transformQuery: { type: Function, default: (e: any) => e },
  // 加载完数据之后，对数据的转换逻辑
  transformListData: { type: Function, default: (e: any) => e },
  loadImmediately: { type: Boolean, default: false }, // 是否立即查询，有些查询需要父组件自行去操作，可以传参false;
  name: { type: String, default: "default" },
});
const emits = defineEmits(["operation"]);

const tableLoading = ref(false);

/* 查询项变化 */
watch(
  () => props.query,
  () => {
    page.pageNum = 1;
    page.reqNum = 1;
    loadData();
  },
  { deep: true }
);
/* 分页 */
const page = reactive({
  pageNum: 1,
  pageSize: 20,
  pageSizes: [20, 25, 50, 100],
  total: 0,
  reqNum: 1,
});

/* 排序 */
const sort = reactive({
  prop: "",
  order: "",
});
//列表项的key
const tableKey = computed(() => `${$app.$route.path}:preview-table:${props.name}`);
/* 列的实际展示 */
const actualColumns = ref([]);
const resetActualColumns = () => {
  localStorage.removeItem(tableKey.value);
  actualColumns.value = genActualColumns(props.columns, []);
};
const genActualColumns = (columns: Array, storage: Array) => {
  const obj = cloneDeep(columns);
  // 分离出 operation 列（如果有）
  const operationColumn = obj.find((column) => column.prop === "operation");
  const nonOperationColumns = obj.filter((column) => column.prop !== "operation").map((column) => ({ ...column, prop: column.prop || column.slotName }));
  // 如果有新加的列（非operation），则设置为选中，顺序放到列的最后面
  nonOperationColumns.forEach((column) => {
    if (!storage.some((item) => item.prop === column.prop)) {
      storage.push({ prop: column.prop, checked: true });
    }
  });
  // 合并配置，保留列顺序（仅对非operation列）
  const mergedNonOperationColumns = nonOperationColumns.map((column) => {
    const storageItem = storage.find((item) => item.prop === column.prop);
    return {
      ...column,
      checked: storageItem ? storageItem.checked : true, // 默认选中
      order: storageItem ? storageItem.order || 0 : storage.length + 1, // 设置顺序
    };
  });
  // 按order排序（仅对非operation列）
  mergedNonOperationColumns.sort((a, b) => a.order - b.order);
  // 如果有operation列，添加到排序后的列末尾
  const mergedColumns = [...mergedNonOperationColumns];
  if (operationColumn) {
    mergedColumns.push({ ...operationColumn, checked: true, order: Number.MAX_SAFE_INTEGER });
  }
  // 保存配置（不保存operation列的配置）
  localStorage.setItem(
    tableKey.value,
    JSON.stringify(
      mergedNonOperationColumns.map((col) => ({
        prop: col.prop,
        checked: col.checked,
        order: col.order,
      }))
    )
  );
  return mergedColumns;
};
// 动态列
watch(
  () => props.columns,
  (val: any[]) => {
    // 获取缓存内容
    const storageObj = dataC.safeObject(localStorage.getItem(tableKey.value));
    const storage = Array.isArray(storageObj) ? storageObj : [];
    // 设置实际显示的列
    actualColumns.value = genActualColumns(props.columns, storage);
  },
  { immediate: true, deep: true }
);
/* 表格 */
const table = reactive({
  tableData: [],
  tableHeight: 0,
});

const viewTableData = computed(() => {
  if (dataC.isEmpty(table.tableData)) return [];
  return table.tableData.slice((page.pageNum - 1) * page.pageSize, page.pageNum * page.pageSize);
});
const selectTotal = computed(() => {
  if (selectAll.value) {
    const disabledLength = table.tableData.filter((item) => item.disabled).length;
    return page.total - disabledLength;
  } else {
    return table.tableData.filter((item) => item.selected).length;
  }
});
const selectCur = computed({
  get() {
    if (table.tableData.length == 0) return false;
    const curTableData = table.tableData.slice((page.pageNum - 1) * page.pageSize, page.pageNum * page.pageSize);
    return curTableData.every((item) => item.disabled || item.selected == true);
  },
  set(val) {
    if (val) {
      events.selectCur();
    } else {
      events.clearAll();
    }
  },
});
const selectAll = computed({
  get() {
    if (table.tableData.length == 0) return false;
    return table.tableData.every((item) => item.disabled || item.selected == true);
  },
  set(val) {},
});
/* 事件 */
const events = reactive({
  handleColumnChange: (key: String, checked: Boolean) => {
    //获取缓存内容
    const storage = dataC.safeObject(localStorage.getItem(tableKey.value)) || [];
    //记录查询项选中情况
    const column = storage.find((item) => item.prop == key);
    column.checked = checked;
    localStorage.setItem(tableKey.value, JSON.stringify(storage));
  },
  handleSortChange: (data: any) => {
    sort.prop = data.prop;
    sort.order = data.order;
    page.pageNum = 1;
    page.reqNum = 1;
    loadData();
  },
  handleOperation: (record: any, operation: any, index: number) => {
    emits(
      "operation",
      assign(
        {},
        { record },
        {
          type: operation.type,
          index,
        }
      )
    );
  },
  currentChange: (val: number) => {
    page.pageNum = val;
    if (props.withSelect) {
      //如果选择全部了，那么切换的时候就不清空
      const selectAll = table.tableData.every((item) => item.disabled || item.selected == true);
      if (selectAll) return;
      table.tableData.forEach((item) => {
        item.selected = false;
      });
    }
  },
  sizeChange: (val: number) => {
    page.pageSize = val;
    if (props.withSelect) {
      //如果选择全部了，那么切换的时候就不清空
      const selectAll = table.tableData.every((item) => item.disabled || item.selected == true);
      if (selectAll) return;
      table.tableData.forEach((item) => {
        item.selected = false;
      });
    }
  },
  continueLoad: () => {
    page.reqNum++;
    loadData();
  },
  selectAll: () => {
    table.tableData.forEach((item) => {
      if (!item.disabled) item.selected = true;
    });
  },
  selectCur: () => {
    events.clearAll();
    const curTableData = table.tableData.slice((page.pageNum - 1) * page.pageSize, page.pageNum * page.pageSize);
    curTableData.forEach((item) => {
      if (!item.disabled) item.selected = true;
    });
  },
  clearAll: () => {
    table.tableData.forEach((item) => {
      item.selected = false;
    });
  },
});

// 操作项“更多”
const collapsedOperations = computed(() => {
  return props.operations.filter((x) => x.collapsed);
});
const noCollapsedOperations = computed(() => {
  return props.operations.filter((x) => !x.collapsed);
});

/* 操作列禁用和隐藏逻辑 */
const isDisabled = (record: any, operation: any) => {
  if (!testAuth() && operation.auth !== "all") return true;
  return typeof operation.disabled === "function" ? !!operation.disabled(record) : !!operation.disabled;
};
const isExit = (record: any, operation: any) => {
  return typeof operation.exist === "function" ? !!operation.exist(record) : true;
};
const getDisabledTips = (record: any, operation: any) => {
  if (!testAuth() && operation.auth !== "all") return "权限不足";
  return typeof operation.disabledTips === "function" ? operation.disabledTips(record) : operation.disabledTips;
};
const getColumnText = (obj, prop) => {
  return getText(_.get(obj, prop));
};

/* 列表接口调用（根据分页和query加载列表数据） */
const loadData = () => {
  tableLoading.value = true;
  const pageData = {
    page: page.reqNum,
    size: 200,
    sort: !dataC.isEmpty(sort.order) ? `${sort.prop.replace("Render", "")},${sort.order.replace("ending", "")}` : "",
  };
  const params = props.transformQuery(assign({}, pageData, props.query));
  return props
    .loadDataApi(params)
    .then((res: any) => {
      const list = props.transformListData(res.content);
      if (props.withSelect) {
        list.forEach((item) => {
          item.selected = false;
        });
      }
      if (page.reqNum == 1) {
        table.tableData = list;
      } else {
        table.tableData.push(...list);
      }
      page.total = table.tableData.length;
      nextTick(() => {
        mediaHeight();
      });
      tableLoading.value = false;
    })
    .catch(() => {
      nextTick(() => {
        mediaHeight();
      });
      tableLoading.value = false;
    });
};

// 计算表格高度
const mediaHeight = () => {
  const contextHeight = proxy.$refs["table-page-wrapper"]?.offsetHeight || 0;
  const queryHeight = proxy.$refs["custom-query"]?.offsetHeight || 0;
  const paginationHeight = proxy.$refs["custom-pagination"]?.offsetHeight || 0;
  table.tableHeight = contextHeight - (queryHeight + paginationHeight + 13);
};

//获取当前分页参数
const getPage = () => {
  return page;
};
//获取当前排序参数
const getSort = () => {
  return sort;
};
//获取表格数据
const getTableData = () => {
  return table.tableData;
};
//获取选择总数
const getSelectTotal = () => {
  return selectTotal.value;
};
//获取是否选择全部
const getSelectAll = () => {
  return selectAll.value;
};

/* 生命周期 */
const debounceFun = debounce(mediaHeight, 200);
onMounted(async () => {
  const debounceFun = debounce(mediaHeight, 200);
  window.addEventListener("resize", debounceFun);
  await nextTick();
  await nextTick(); //不连续使用两次选择器找不到column-popover的dom
  Sortable.create(document.querySelector(`.column-popover #column-checkbox-list-${props.name}`) as any, {
    animation: 500,
    sort: true,
    //拖拽结束后触发
    onEnd: (evt) => {
      // 获取拖拽后的新顺序
      const newIndex = evt.newIndex;
      const oldIndex = evt.oldIndex;
      // 更新数组顺序
      const storage = dataC.safeObject(localStorage.getItem(tableKey.value));
      const movedItem = storage.splice(oldIndex, 1)[0];
      storage.splice(newIndex, 0, movedItem);
      storage.forEach((item, index) => (item.order = index + 1));
      actualColumns.value = genActualColumns(props.columns, storage);
    },
  });
});
onUnmounted(() => {
  window.removeEventListener("resize", debounceFun);
});

/* 抛出所有的原生方法 */
const myTableRef = ref<any>(null);
const defaultExposes = [
  "clearSelection",
  "getSelectionRows",
  "toggleRowSelection",
  "toggleAllSelection",
  "toggleRowExpansion",
  "setCurrentRow",
  "clearSort",
  "clearFilter",
  "doLayout",
  "sort",
  "scrollTo",
  "setScrollTop",
  "setScrollLeft",
];
const setExposes = () => {
  const exposes: any = {};
  defaultExposes.forEach((key: string) => {
    exposes[key] = (...args: any) => {
      myTableRef.value[key](...args);
    };
  });
  return exposes;
};
if (props.loadImmediately) {
  loadData();
}
defineExpose({
  ...setExposes(),
  loadData,
  mediaHeight,
  getPage,
  getSort,
  getTableData,
  getSelectTotal,
  getSelectAll,
});
</script>

<style lang="scss" scoped>
.table-page-wrapper {
  height: 100%;
  > div {
    @include area-padding;
    background: #fff;
  }
  .header-wrapper {
    padding-bottom: 0px;
  }
  .query-wrapper {
    padding-bottom: 0px;
  }
  .table-wrapper {
    padding-top: 0;
  }
  .pagination-wrapper {
    position: relative;
    @include flex();
    justify-content: flex-end;
    padding-top: 0;
  }
}
.icon-copy {
  color: $primary-color;
  margin-left: 5px;
  cursor: pointer;
}
.edit-input-text {
  cursor: pointer;
  > .icon {
    display: none;
    margin-left: 4px;
    color: $primary-color;
  }
  &:hover {
    > .icon {
      display: inline-block;
    }
  }
}
::v-deep .draggable-row {
  cursor: pointer;
}
.flex {
  .btn-title {
    width: calc(100% - 30px);
    text-align: left;
    margin: 0 5px;

    > span {
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
::v-deep .el-checkbox:last-of-type {
  height: 20px;
}
.column-popover {
  .el-checkbox:last-of-type {
    height: 25px;
  }
}
</style>
