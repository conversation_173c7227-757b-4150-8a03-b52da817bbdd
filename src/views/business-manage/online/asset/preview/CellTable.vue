<template>
  <div class="data-cell-table">
    <PreviewDetail ref="previewDetailRef" v-if="showPreviewDetail" @close="showPreviewDetail = false" :operationAuth="operationAuth" />
    <MyTable
      ref="myTableRef"
      :name="`cell-table-${auditStatus}`"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformQuery="transformQuery"
      :transformListData="transformListData"
      :withSort="true"
      :withSelect="true"
      :operations="operations"
      @operation="handleOperation"
      :operationAuth="operationAuth"
      :loadImmediately="false"
    >
      <template #query>
        <MyQuery ref="myQueryRef" :name="`cell-query-${auditStatus}`" :queryItems="queryItems" @search="events.search" @reset="events.reset" />
      </template>
      <template #pagination>
        <div class="pagination-info">
          <span title="筛选后数据总量,审核状态和筛选条件会影响此数值">筛选后数据总量: {{ util.formatNumber(modelValue.dataItemFilterCount) }} </span>
        </div>
      </template>
      <template #_id="scope">
        <div class="flex">
          <el-button link type="primary" @click="events.previewData(scope.row)" class="btn-title">
            {{ scope.row._id }}
          </el-button>
          <el-icon class="icon-copy" @click="copyText(scope.row._id)">
            <CopyDocument />
          </el-icon>
        </div>
      </template>
    </MyTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, cloneDeep, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import { copyText } from "@/utils/helpers";
import MyTable from "../MyTable.vue";
import MyQuery from "../MyQuery.vue";
import PreviewDetail from "./PreviewDetail.vue";
import useCtx from "@/hooks/useCtx";
import * as dataAssetApi from "@/api/data-asset";
import * as util from "@/utils/common";

const { $app, proxy, $router, $auth } = useCtx();

const emit = defineEmits(["update-count"]);

const props = defineProps({
  operationAuth: { type: String },
  auditStatus: { type: Number },
  schema: { type: Object },
  fieldItemList: { type: Array, default: [] },
});

const myTableRef = ref(null);
const previewDetailRef = ref(null);
const myQueryRef = ref(null);
const showPreviewDetail = ref(false);
const rowColumnSet = ref(new Set());
const query = ref({});
const queryDsl = ref("");

const modelValue = reactive({
  dataItemCount: "计算中",
  dataItemFilterCount: "计算中",
});

//没有cellFields时，使用这个配置
const defaultQueryItems = ref({
  _id: {
    fieldType: "LONG",
    style: "input",
    label: "_id",
    labelHidden: true,
    modelValue: "",
    defaultValue: "",
    operateValue: "EQ",
  },
  domain: {
    fieldType: "STRING",
    style: "input",
    label: "domain",
    labelHidden: true,
    modelValue: "",
    defaultValue: "",
    operateValue: "EQ",
  },
  url: {
    fieldType: "STRING",
    style: "input",
    label: "url",
    labelHidden: true,
    modelValue: "",
    defaultValue: "",
    operateValue: "EQ",
  },
  "_x.dbg": {
    fieldType: "INT",
    style: "select",
    label: "调试数据",
    labelHidden: true,
    modelValue: "",
    defaultValue: "",
    operateValue: "EQ",
    options: [
      { label: "调试数据", value: 1 },
      { label: "非调试数据", value: 0 },
    ],
    attrs: { placeholder: "是否为调试数据" },
    hidden: computed(() => ["WEB", "DOC"].includes($router.currentRoute.value.query.kind)),
  },
  "_x.op": {
    fieldType: "INT",
    style: "select",
    label: "待删除数据",
    labelHidden: true,
    modelValue: "",
    defaultValue: "",
    operateValue: "EQ",
    options: [{ label: "待删除数据", value: 3 }],
    attrs: { placeholder: "是否为待删除数据" },
  },
  "_x.s": {
    fieldType: "INT",
    style: "select",
    label: "重复数据",
    labelHidden: true,
    modelValue: "",
    defaultValue: "",
    operateValue: "EQ",
    options: [
      { label: "非重复数据", value: 0 },
      { label: "重复数据", value: 1 },
    ],
    attrs: { placeholder: "是否为重复数据" },
  },
  "_x.uts": {
    fieldType: "LONG",
    style: "datetime",
    label: "更新时间",
    labelHidden: true,
    modelValue: [],
    defaultValue: [],
  },
});

//是否为动态查询项
const isDynamicQuery = computed(() => {
  return props.schema.cellFields?.some((item) => item.filterConfig);
});

//动态查询项配置，根据桶的字段cellFields配置，但是字段的筛选项读取数据规格属性里最新的
const queryItems = computed(() => {
  if (isDynamicQuery.value) {
    const config = {};
    props.schema.cellFields.forEach((configItem) => {
      //const fieldItem = props.fieldItemList.find((x) => x.fieldCode === configItem.fieldCode) || configItem;
      const fieldItem = configItem;
      let filterConfig = {};
      try {
        filterConfig = JSON.parse(fieldItem.filterConfig);
      } catch (error) {}
      if (fieldItem.supportFilter) {
        config[fieldItem.fieldCode] = {
          fieldType: fieldItem.fieldType.toUpperCase(),
          style: filterConfig.style || "input",
          label: fieldItem.name,
          labelHidden: true,
          modelValue: "",
          defaultValue: "",
          operateValue: "EQ",
          options: filterConfig.selectOptions || [],
          valueOptions: filterConfig.valueOptions || [],
        };
      }
    });
    return config;
  } else {
    return defaultQueryItems.value;
  }
});

const selectAll = computed(() => myTableRef.value?.getSelectAll() || false);

const columns = computed(() => {
  // 基础列定义
  const baseColumns = [
    { prop: "_id", label: "_id", width: 220, slotName: "_id", fixed: "left" },
    { prop: "_x.utsRender", label: "更新时间", width: 180 },
    { prop: "_x.auRender", label: "审核人", width: 100 },
    { prop: "_x.atRender", label: "审核时间", width: 180 },
    { prop: "_x.sRender", label: "是否重复数据", width: 140 },
  ];
  // 如果有_ref字段，添加到基础列
  if (rowColumnSet.value.has("_ref")) {
    baseColumns.push({ prop: "_ref", label: "_ref", width: 220, withCopy: true });
  }
  // 获取schema中定义的字段顺序
  const schemaFields = props.schema?.cellFields || [];
  // 按照schema中的顺序创建动态列
  const orderedColumns = [];
  // 1. 首先添加schema中有且rowColumnSet中也存在的字段
  schemaFields.forEach((field) => {
    const fieldCode = field.fieldCode;
    if (rowColumnSet.value.has(fieldCode)) {
      const isTime = fieldCode.endsWith("_ts");
      orderedColumns.push({
        prop: isTime || ["levels"].includes(fieldCode) ? `${fieldCode}Render` : fieldCode,
        label: field.name || fieldCode,
        minWidth: 180,
      });
    }
  });
  // 2. 然后添加rowColumnSet中有但schema中没有的字段
  rowColumnSet.value.forEach((key) => {
    // 跳过已经处理过的字段和以_开头的内部字段
    if (!key.startsWith("_") && !schemaFields.some((f) => f.fieldCode === key)) {
      const isTime = key.endsWith("_ts");
      orderedColumns.push({
        prop: isTime || ["levels"].includes(key) ? `${key}Render` : key,
        label: key,
        minWidth: 180,
      });
    }
  });
  // 合并所有列
  const finalColumns = [...baseColumns, ...orderedColumns];
  // 添加操作列（如果有）
  if (!dataC.isEmpty(props.schema.operations)) {
    finalColumns.push({ prop: "operation", label: "操作", width: 110, fixed: "right" });
  }
  return finalColumns;
});

const operations = computed(() =>
  props.schema?.operations?.map((item) => ({
    type: item.value,
    label: item.name,
    collapsed: !["correct", "delete"].includes(item.value),
    btnType: ["delete"].includes(item.value) ? "danger" : "primary",
  }))
);

// 方法
const loadListData = async (data: any) => {
  modelValue.dataItemFilterCount = "计算中";
  //查询数据
  const result = await dataAssetApi.getCellListPage(data);
  //获取筛选后数据总量
  getDataItemFilterCount(data);
  //获取数据字段集合以便生成动态列
  rowColumnSet.value.clear();
  for (let i = 0; i < 10 && i < result.data.content.length; i++) {
    keys(result.data.content[i]).forEach((key) => {
      rowColumnSet.value.add(key);
    });
  }
  //返回结果
  return result.data;
};

const transformQuery = (data: any) => {
  const numberType = ["INT", "DOUBLE", "FLOAT"];
  const params = {
    auditStatus: props.auditStatus,
    bucketCode: $router.currentRoute.value.query.bucket,
    regionCode: $router.currentRoute.value.query.region,
    page: data.page,
    size: data.size,
    sort: data.sort,
  };
  const inputList = [];
  for (const [field, fieldItem] of Object.entries(queryItems.value)) {
    const fieldType = fieldItem.fieldType;
    if (!dataC.isEmpty(data[field]?.modelValue)) {
      const input = { field: field, "@type": fieldType };
      if (fieldItem.style === "datetime") {
        const time1 = new Date(data[field].modelValue[0] + " GMT+0800").getTime();
        const time2 = new Date(data[field].modelValue[1] + " GMT+0800").getTime();
        if (fieldType === "INT") {
          inputList.push({ ...input, op: "GE", args: [time1 / 1000] });
          inputList.push({ ...input, op: "LE", args: [time2 / 1000] });
        } else {
          inputList.push({ ...input, op: "GE", args: [time1] });
          inputList.push({ ...input, op: "LE", args: [time2] });
        }
      } else {
        inputList.push({
          ...input,
          op: data[field].operateValue,
          args: [numberType.includes(fieldType) ? parseFloat(data[field].modelValue) : data[field].modelValue],
        });
      }
    }
  }
  if (!dataC.isEmpty(data.extraQuery)) {
    data.extraQuery?.forEach((extraItem) => {
      let fieldType = queryItems.value[extraItem.keyValue]?.fieldType;
      if (!fieldType) {
        try {
          parseFloat(extraItem.modelValue);
          fieldType = "INT";
        } catch (error) {
          fieldType = "STRING";
        }
      }
      inputList.push({
        field: extraItem.keyValue,
        "@type": fieldType,
        op: extraItem.operateValue,
        args: [numberType.includes(fieldType) ? parseFloat(extraItem.modelValue) : extraItem.modelValue],
      });
    });
  }
  params.dsl = JSON.stringify({ nodes: inputList, "@type": "AND" });
  //记录dsl语句供批量操作时使用
  queryDsl.value = params.dsl;
  return params;
};

const transformListData = (data: any) => {
  const levelsFieldItem = props.fieldItemList.find((item) => item.fieldCode === "levels");
  let levelsKeyOptions = [];
  let levelsValueOptions = [];
  try {
    const levelsFilterConfig = JSON.parse(levelsFieldItem.filterConfig);
    levelsKeyOptions = levelsFilterConfig.selectOptions;
    levelsValueOptions = levelsFilterConfig.valueOptions.map((item) => ({ label: item.label, value: parseInt(item.value) }));
  } catch (error) {}
  return data.map((x: any) => {
    x._x.utsRender = timeC.format(x._x.uts, "YYYY-MM-DD hh:mm:ss");
    x._x.auRender = x._x.au;
    x._x.atRender = timeC.format(x._x.at, "YYYY-MM-DD hh:mm:ss");
    x._x.sRender = x._x.s === 1 ? "重复数据" : "非重复数据";
    keys(x).forEach((key) => {
      if (key.endsWith("_ts") && x[key]) {
        x[`${key}Render`] = timeC.format(x[key].toString().length === 10 ? x[key] * 1000 : x[key], "YYYY-MM-DD hh:mm:ss");
      } else if (key === "levels" && x["levels"]) {
        x.levelsRender = keys(x.levels)?.map((key) => {
          const label = dataC.getItemByValue(levelsKeyOptions, `levels.${key}`, "value")?.label || key;
          const value = dataC.getItemByValue(levelsValueOptions, x.levels[key], "value")?.label || x.levels[key];
          return `${label}: ${value}`;
        });
      }
    });
    return x;
  });
};

const getDataItemFilterCount = (data: any) => {
  dataAssetApi.getCellCount(data).then((result) => {
    modelValue.dataItemFilterCount = result.data;
    if (modelValue.dataItemCount === "计算中") {
      modelValue.dataItemCount = result.data;
      emit("update-count", props.auditStatus, result.data);
    }
  });
};

const loadList = () => {
  myTableRef.value?.loadData();
};

const handleOperation = (data: any) => {
  const { type, record } = data;
  const operationMap = {
    delete: events.delete,
  };

  if (operationMap[type]) {
    operationMap[type](record);
  } else {
  }
};

// 事件
const events = {
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {
    Object.keys(obj).forEach((key) => {
      queryItems.value[key].modelValue = obj[key].defaultValue;
    });
    modelValue.dataItemCount = "计算中";
    emit("update-count", props.auditStatus, "计算中");
  },
  previewData: (record: any) => {
    showPreviewDetail.value = true;
    nextTick(() => {
      previewDetailRef.value?.refreshList(myTableRef.value?.getTableData(), myTableRef.value?.getPage(), {
        auditStatus: props.auditStatus,
        kindCode: $router.currentRoute.value.query.kind,
        bucketCode: $router.currentRoute.value.query.bucket,
        regionCode: $router.currentRoute.value.query.region,
        indexId: $router.currentRoute.value.query.index,
        schema: props.schema,
        dataItemFilterCount: modelValue.dataItemFilterCount,
      });
      previewDetailRef.value?.refreshContent(record);
    });
  },
  delete: async (record: any) => {
    try {
      await $app.$confirm({
        title: `确认要删除【${record.title || record.fileName || record._id}】吗?`,
      });
      await dataAssetApi.deleteCell({ bucketCode: $router.currentRoute.value.query.bucket, auditStatus: props.auditStatus, ids: [record._id] });
      loadList();
      $app.$message.success(`删除成功`);
    } catch {
      // 用户取消或操作失败
    }
  },
};

// 初始化
onMounted(() => {});

const getSelectTotal = () => (selectAll.value ? modelValue.dataItemFilterCount : myTableRef.value?.getSelectTotal() || 0);

// 公共函数：获取选中的ID列表
const getSelectedIds = () => {
  return selectAll.value
    ? []
    : myTableRef.value
        .getTableData()
        .filter((item) => item.selected)
        .map((item) => item._id);
};

// 公共函数：执行批量操作
const executeBatchOperation = async (operation) => {
  try {
    const { confirmMessage, apiMethod, params, successMessage } = operation;
    await $app.$confirm({ title: confirmMessage });
    await apiMethod(getOperateBatchParams(), ...(params || []));
    loadList();
    $app.$message.success(successMessage);
  } catch (error) {}
};

const approveBatch = () =>
  executeBatchOperation({
    confirmMessage: "确认要审核通过吗?",
    apiMethod: dataAssetApi.auditCell,
    params: [1],
    successMessage: "审核通过成功",
  });

const rejectBatch = () =>
  executeBatchOperation({
    confirmMessage: "确认要审核不通过吗?",
    apiMethod: dataAssetApi.auditCell,
    params: [0],
    successMessage: "审核不通过成功",
  });

const deleteBatch = () =>
  executeBatchOperation({
    confirmMessage: "确认要批量删除吗?",
    apiMethod: dataAssetApi.deleteCell,
    successMessage: "批量删除成功",
  });

const download = async () => {
  try {
    await $app.$confirm({ title: `确认要导出吗?` });
    const result = await dataAssetApi.exportCell(getOperateBatchParams());
    util.downloadFile(result, `数据单元数据.xlsx`);
  } catch {
    // 用户取消或操作失败
  }
};

const getOperateBatchParams = () => {
  //查询的dsl
  let dsl = queryDsl.value;
  //如果不是选择全部，则将批量选择的id组合为dsl
  if (!selectAll.value && getSelectedIds().length > 0) {
    dsl = JSON.stringify({ nodes: [{ field: "_id", "@type": "LONG", op: "IN", args: getSelectedIds() }], "@type": "AND" });
  }
  const queryParams = {
    dsl,
    auditStatus: props.auditStatus,
    bucketCode: $router.currentRoute.value.query.bucket,
    regionCode: $router.currentRoute.value.query.region,
  };
  return queryParams;
};

defineExpose({ loadList, getSelectTotal, approve: approveBatch, reject: rejectBatch, delete: deleteBatch, download });
</script>

<style lang="scss" scoped>
.data-cell-table {
  height: 100%;

  ::v-deep {
    .query-wrapper,
    .table-wrapper {
      padding: 0 !important;
    }
  }

  .btn-title {
    width: calc(100% - 30px);
    text-align: left;
    margin: 0 5px;

    > span {
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .icon-copy {
    color: $primary-color;
    margin-left: 5px;
    cursor: pointer;
  }

  .pagination-info {
    font-size: 16px;
    font-weight: 700;
    margin-right: 15px;
    white-space: nowrap;

    span + span {
      margin-left: 20px;
    }

    span {
      white-space: nowrap;
    }
  }
}
</style>
