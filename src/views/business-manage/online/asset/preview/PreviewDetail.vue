<template>
  <FullscreenPage title="数据预览" @close="events.close" class="preview-detail-full-screen-page">
    <template #header>
      <div style="display: flex">
        <div v-if="selectTotal > 0" class="total-box">
          {{ $t("title.selected") }}
          <span>{{ selectTotal }}</span>
          {{ $t("title.item") }}
        </div>
        <my-button v-if="showApproveButton" type="primary" :icon="Finished" :operationAuth="operationAuth" :disabled="selectTotal <= 0">审核通过</my-button>
        <my-button v-if="showRejectButton" type="warning" :icon="Remove" :operationAuth="operationAuth" :disabled="selectTotal <= 0">审核不通过</my-button>
      </div>
    </template>

    <div class="preview-detail flexBetween">
      <!-- 左侧菜单 -->
      <div class="left-wrap">
        <div class="top">
          <el-checkbox v-model="selectCur" :value="true" />
          <el-dropdown trigger="click">
            <el-button link :icon="ArrowDownBold" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="events.selectAll">
                  <div>
                    <el-icon v-if="selectAll"><Select /></el-icon>
                    <span :style="{ marginLeft: selectAll ? '0' : '19px' }">选择全部</span>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item @click="events.selectCur">
                  <div>
                    <el-icon v-if="!selectAll && selectCur"><Select /></el-icon>
                    <span :style="{ marginLeft: !selectAll && selectCur ? '0' : '19px' }">选择当前页</span>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item @click="events.clearAll">
                  <span style="margin-left: 19px">清除选择</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <ul class="menu-wrap">
          <li
            v-for="(item, index) in viewTableData"
            :key="item._id"
            class="menu-item"
            :class="{ active: item._id === modelValue.showDataItem._id }"
            @click="refreshContent(item)"
          >
            <el-checkbox v-model="item.selected" :value="true" />
            <span :title="item._id">{{ getItemIndex(index) }}.&nbsp;{{ isFile ? item.fileName : item.title || item._id }}</span>
          </li>
        </ul>

        <div class="pagination-wrap">
          <el-pagination
            :current-page="page.page"
            :page-size="page.size"
            :total="page.total"
            layout="prev, pager, next"
            :pager-count="4"
            @current-change="events.handleCurrentChange"
          />
        </div>
      </div>

      <!-- 中间内容区域 -->
      <div class="center-wrap height-adaptive" :style="centerWrapStyle">
        <h2 class="title" @click="openOriginData">
          {{ isFile ? modelValue.showDataItem.fileName : modelValue.showDataItem.title || modelValue.showDataItem._id }}
        </h2>

        <div class="main" :style="{ padding: isFile ? '0' : '16px' }">
          <template v-if="isRegion">
            <el-row>
              <el-radio-group v-model="modelValue.contentOrigin">
                <el-radio-button label="mongo" value="mongo" />
                <el-radio-button label="es" value="es" />
              </el-radio-group>
            </el-row>
            <div class="json-viewer-container">
              <json-viewer :value="contentDoc" expanded :expand-depth="5" copyable boxed sort></json-viewer>
            </div>
          </template>
          <template v-else>
            {{ modelValue.content.mongoDoc.content }}
          </template>
        </div>
      </div>

      <!-- 右侧属性区域 -->
      <div v-if="!isRegion" class="right-wrap">
        <div class="top">
          <div>文档属性</div>
        </div>

        <div class="main">
          <div v-for="item in columns" :key="item.key" class="detail-item">
            <div>
              <label :class="getLabelClass(item.tag)"> {{ `${item.label}(${item.key})` }} : </label>
              {{ modelValue.showDataItem[item.prop] }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </FullscreenPage>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { keys } from "lodash";
import { dataC } from "turing-plugin";
import FullscreenPage from "@/components/layout/FullscreenPage.vue";
import JsonViewer from "vue-json-viewer";
import { ArrowDownBold, Finished, Remove } from "@element-plus/icons-vue";
import * as dataAssetApi from "@/api/data-asset";
import * as util from "@/utils/common";

const props = defineProps({
  operationAuth: { type: String },
});

// 响应式数据
const modelValue = reactive({
  dataItemList: [],
  showDataItem: {},
  auditStatus: null,
  kindCode: "",
  bucketCode: "",
  schema: {},
  dataItemFilterCount: 0,
  contentOrigin: "mongo",
  content: {
    mongoDoc: {},
    esDoc: {},
  },
});

const page = reactive({
  page: 1,
  size: 20,
  total: 0,
});

// 计算属性
const isFile = computed(() => modelValue.kindCode === "DOC");
const isRegion = computed(() => modelValue.kindCode === "IDX");
const showApproveButton = computed(() => modelValue.auditStatus === -1 || modelValue.auditStatus === -2 || modelValue.auditStatus === 0);
const showRejectButton = computed(() => modelValue.auditStatus === -1 || modelValue.auditStatus === -2 || modelValue.auditStatus === 1);
const contentDoc = computed(() => modelValue.content[`${modelValue.contentOrigin}Doc`]);

const viewTableData = computed(() => {
  if (dataC.isEmpty(modelValue.dataItemList)) return [];
  const start = (page.page - 1) * page.size;
  return modelValue.dataItemList.slice(start, start + page.size);
});

const selectAll = computed(() => modelValue.dataItemList.length > 0 && modelValue.dataItemList.every((item) => item.selected));

const selectCur = computed({
  get() {
    return viewTableData.value.length > 0 && viewTableData.value.every((item) => item.selected);
  },
  set(val) {
    val ? events.selectCur() : events.clearAll();
  },
});

const selectTotal = computed(() => {
  if (selectAll.value) return modelValue.dataItemFilterCount;
  return modelValue.dataItemList.filter((item) => item.selected).length;
});

const columns = computed(() => {
  const excludeKeys = ["content", "ss", "embedding"];
  const list = [{ key: "_id", prop: "_id", label: "_id", tag: "A" }];

  keys(modelValue.showDataItem).forEach((key) => {
    if (!key.endsWith("Render") && !excludeKeys.includes(key)) {
      const isTime = key.endsWith("_ts");
      const obj = dataC.getItemByValue(modelValue.schema.cellFields || [], key, "fieldCode");
      if (!key.startsWith("_") || obj.name) {
        list.push({
          key,
          prop: isTime || ["levels"].includes(key) ? `${key}Render` : key,
          label: obj.name || key,
          tag: "A",
        });
      }
    }
  });

  return list;
});

const centerWrapStyle = computed(() => ({
  width: isRegion.value ? "calc(100% - 314px)" : "calc(100% - 680px)",
  marginRight: isRegion.value ? "0" : "",
  flex: isRegion.value ? "" : "1",
}));

// 方法
const getItemIndex = (index) => (page.page - 1) * page.size + index + 1;
const getLabelClass = (tag) => ({
  "blue-text": tag === "B",
  "red-text": tag === "C",
});

const openOriginData = () => {
  if (isFile.value) {
    dataAssetApi.downloadDoc(modelValue.bucketCode, modelValue.showDataItem._id).then((res) => {
      util.downloadFile(res, modelValue.showDataItem.fileName);
    });
  } else {
    const item = modelValue.showDataItem;
    window.open(item.url || util.displayUrl(item.protocol, item.domain, item.path), "_blank");
  }
};

// 事件处理
const events = {
  handleCurrentChange: (val) => {
    page.page = val;
    events.clearAll();
  },
  originData: openOriginData,
  close: () => emits("close"),
  selectAll: () => modelValue.dataItemList.forEach((item) => (item.selected = true)),
  selectCur: () => {
    events.clearAll();
    viewTableData.value.forEach((item) => (item.selected = true));
  },
  clearAll: () => modelValue.dataItemList.forEach((item) => (item.selected = false)),
};

// 公共方法
const refreshList = (tableData, pageData, ext) => {
  Object.assign(modelValue, {
    dataItemList: tableData,
    auditStatus: ext.auditStatus,
    kindCode: ext.kindCode,
    bucketCode: ext.bucketCode,
    regionCode: ext.regionCode,
    indexId: ext.indexId,
    schema: ext.schema,
    dataItemFilterCount: ext.dataItemFilterCount,
  });

  Object.assign(page, {
    page: pageData.pageNum,
    size: pageData.pageSize,
    total: pageData.total,
  });
};

const refreshContent = (dataItem) => {
  modelValue.showDataItem = dataItem;
  dataAssetApi.getCellDetail(dataItem._id, modelValue.bucketCode, modelValue.indexId, modelValue.regionCode).then((result) => {
    modelValue.content.mongoDoc = result.mongoDoc;
    modelValue.content.esDoc = result.esDoc;
  });
};

// 事件声明
const emits = defineEmits(["close"]);

// 暴露接口
defineExpose({ refreshList, refreshContent });
</script>

<style lang="scss" scoped>
.preview-detail {
  height: 100%;

  > div {
    height: 100%;
    border: 1px solid $primary-color;
    border-radius: 8px;
  }

  .left-wrap {
    width: 300px;

    .top {
      padding: 6px 24px;
      border-bottom: 1px solid $primary-color;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .menu-wrap {
      padding: 16px 12px;
      overflow-y: auto;
      @include calc-height(82px);

      .menu-item {
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        @include no-wrap();
        display: flex;
        align-items: center;
        gap: 8px;

        & + .menu-item {
          margin-top: 5px;
        }

        &.active {
          background-color: rgba(75, 114, 239, 0.05);
          color: $primary-color;
        }

        &:hover {
          background-color: #f5f5f5;
        }

        > span {
          width: 100%;
          display: block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .pagination-wrap {
      height: 52px;
      padding: 10px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .right-wrap {
    width: 380px;

    .top {
      border-bottom: 1px solid $primary-color;
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      font-weight: bold;
    }

    .main {
      @include calc-height(40px);
      overflow-y: auto;
      padding: 16px;

      .detail-item {
        margin-bottom: 12px;

        label {
          font-weight: bold;

          &.blue-text {
            color: #029dfe;
          }

          &.red-text {
            color: #fe4848;
          }
        }
      }
    }
  }

  .center-wrap {
    padding: 16px;
    margin: 0 16px;

    .title {
      padding: 12px 0;
      text-decoration: underline;
      color: $primary-color;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }

    .main {
      height: 100%;
      overflow: auto;
      background-color: rgba(75, 114, 239, 0.05);
      padding: 16px;
      border-radius: 8px;
      font-size: 16px;
      line-height: 28px;
    }

    .json-viewer-container {
      height: calc(100% - 55px);
      margin-top: 20px;
    }
  }
}

.total-box {
  height: 32px;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: $text-color-secondary;
  margin-right: 12px;

  span {
    color: $primary-color;
    margin: 0 6px;
  }
}
</style>

<style lang="scss">
.preview-detail {
  .el-pagination {
    background-color: transparent;

    > button,
    > ul,
    > ul > li {
      background-color: transparent !important;
    }
  }

  .jv-container .jv-code.boxed {
    max-height: calc(100vh - 235px);
    height: calc(100vh - 235px);
  }

  .el-checkbox:last-of-type {
    height: 20px;
  }
}

.preview-detail-full-screen-page {
  .fullscreen-page__header {
    justify-content: flex-start !important;
  }

  .preview-detail-query {
    margin-left: 185px;

    .el-form-item {
      margin-bottom: 0 !important;
    }
  }
}
</style>
