<template>
  <div class="dedup-edit" style="width: 100%">
    <h3 class="common-part-title" style="margin-bottom: 16px; margin-top: 10px">去重规则</h3>
    <my-select v-model="metaDedupId" :options="ruleOptions" value-in-label @change="ruleChange" :disabled="readOnly" />
    <template v-if="!dataC.isEmpty(metaDedupId)">
      <h3 class="common-part-title" style="margin-bottom: 16px; margin-top: 10px">入参</h3>
      <paramUse :key="pramUseKey" :inputArgs="inputArgs" :readOnly="readOnly" />
      <h3 class="common-part-title" style="margin-bottom: 16px; margin-top: 10px">脚本</h3>
      <CustomCodemirror ref="codeMirrorDef" readOnly :domId="`domId${Date.now()}`" :initVal="`加载脚本中...`" height="500px" />
    </template>
    <el-dialog v-model="visible" title="提示" width="500">
      <span>当前去重规则发生更改，是否需要更新？</span>
      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from "vue";
import { cloneDeep } from "lodash";
import { dataC } from "turing-plugin";
import paramUse from "@/views/common/paramComp/paramUse.vue";
import CustomCodemirror from "@/views/common/paramComp/components/CustomCodemirror.vue";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";

const { $app, proxy } = useCtx();
const { api } = useStore();

const props = defineProps({
  readOnly: { type: Boolean, default: false },
});

const emits = defineEmits(["update:modelValue"]);
const visible = ref(false);
const metaDedupId = ref("");
const metaDedupName = ref("");
const script = ref("");
const inputArgs = ref<any>([]);
const pramUseKey = ref(0);
const ruleOptions = ref<any>([]);
const codeMirrorDef = ref();
const oldData = ref();
const newData = ref();

const getRuleOptions = async () => {
  const region = await api.getVerifyRegion();
  ruleOptions.value = await api.getMetaDedupOptions(region);
};

const ruleChange = async (obj: any, type?: string) => {
  if (dataC.isEmpty(obj)) {
    [metaDedupId.value, metaDedupName.value, script.value, inputArgs.value] = ["", "", "", ""];
  } else {
    metaDedupId.value = obj.id;
    metaDedupName.value = obj.name;
    script.value = obj.script;
    inputArgs.value = obj.inputArgs?.map((v) => ({ ...v, value: type !== "old" ? v.defaultValue : v.value })) || [];
    pramUseKey.value += 1;
  }
  await nextTick();
  codeMirrorDef.value?.updateCodemirrorVal(script.value);
};

const close = () => {
  ruleChange(oldData.value, "old");
  visible.value = false;
};
const confirm = () => {
  ruleChange(newData.value);
  visible.value = false;
};

const handleUpdate = async (oldVal: any) => {
  [oldData.value, newData.value] = [undefined, undefined];
  await getRuleOptions();

  if (dataC.isEmpty(oldVal?.metaDedupId)) return ruleChange();

  const obj = dataC.getItemByValue(ruleOptions.value, oldVal.metaDedupId, "id");
  if (dataC.isEmpty(obj.id)) {
    $app.$message.warning("历史去重配置已不可用!");
    return ruleChange();
  }

  newData.value = cloneDeep(obj);
  oldData.value = { ...oldVal, id: oldVal.metaDedupId, name: oldVal.metaDedupName };

  if (oldData.value.script !== obj.script || !arraysAreEqual(oldData.value.inputArgs, obj.inputArgs)) {
    visible.value = true;
  } else if (oldData.value?.name !== obj.name && oldData.value?.name) {
    $app.$message.warning("规则名称发生改变");
    ruleChange(oldData.value, "old");
  } else {
    ruleChange(oldData.value, "old");
  }
};

const arraysAreEqual = (a, b) => a?.every((item1) => b?.some((item2) => item1.key === item2.key));

const getFormData = () => {
  const script = getScript();
  if (!script) return false;
  const scriptParam = getScriptParam();
  if (!scriptParam) return false;
  return {
    metaDedupId: metaDedupId.value,
    metaDedupName: metaDedupName.value,
    inputArgs: inputArgs.value,
    script,
    scriptParam,
  };
};

// 表单验证
const validateFormData = (data: any) => {};

const getScript = () => {
  if (!script.value) {
    $app.$message.warning("去重脚本不可为空!");
    return false;
  } else {
    return script.value;
  }
};
const getScriptParam = () => {
  const param = {};
  for (const item of inputArgs.value) {
    if (item.required && !item.value) {
      $app.$message.warning(`${item.name}不可为空!`);
      return false;
    }
    param[item.key] = item.value;
  }
  if (Object.keys(param).length === 0) {
    $app.$message.warning("去重规则不可为空!");
    return false;
  }
  if (param.scene !== "医疗") {
    const fields = param.fields?.split(",") || [];
    for (const field of fields) {
      if (!param[field]) {
        $app.$message.warning(`${field}不可为空!`);
        return false;
      }
    }
  }
  if (Array.isArray(param.levels)) {
    param.levels = param.levels.join(",");
  }
  return param;
};

defineExpose({ handleUpdate, getFormData });
</script>
