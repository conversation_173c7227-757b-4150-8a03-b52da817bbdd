<template>
  <el-dialog title="失败详情" v-model="dialogVisible" fullscreen :show-close="false" class="fail-detail">
    <template #header>
      <div class="flex">
        <span class="header-back" @click="dialogVisible = false">
          <el-icon><Back /></el-icon>
        </span>
        <h3>【{{ bucketInfo.bucketName }}】失败数据详情</h3>
      </div>
    </template>
    <div class="fail-detail-table">
      <table-page
        ref="myTableRef"
        name="fail-detail"
        :columns="columns"
        :query="query"
        :loadDataApi="loadListData"
        :transformQuery="transformQuery"
        :transformListData="transformListData"
        :loadImmediately="false"
      >
        <template #query>
          <div class="flexBetweenStart">
            <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
            <my-button type="export" @click="events.exportExcel">导出</my-button>
          </div>
        </template>
      </table-page>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { keys, assign } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import { Back } from "@element-plus/icons-vue";
import * as dataAssetApi from "@/api/data-asset";
import * as util from "@/utils/common";
import useStore from "@/store";

const { $app, proxy } = useCtx();
const { api } = useStore();

const dialogVisible = ref(false);
const myTableRef = ref(null);
const bucketInfo = ref({});
const executionInfo = ref({});
const rowExample = ref({});
const failDetailCount = ref(0);

//列配置
const columns = computed(() => {
  const list = [
    { prop: "_id", label: "_id", width: 220, withCopy: true, fixed: "left" },
    { prop: "codeRender", label: "错误码", width: 220, fixed: "left" },
  ];

  keys(rowExample.value).forEach((key) => {
    if (key !== "selected" && key !== "_id" && key !== "code" && !key.endsWith("Render")) {
      const isTime = key.endsWith("_ts");
      if (!key.startsWith("_")) {
        list.push({
          prop: isTime ? `${key}Render` : key,
          label: key,
          minWidth: 180,
        });
      }
    }
  });

  return list;
});
//查询面板
const query = ref<any>({
  code: "",
});
const queryItems = ref<any>({
  code: {
    type: "select",
    label: "",
    modelValue: "",
    defaultValue: "",
    width: "240px",
    options: [],
    attrs: {
      placeholder: "请选择错误码",
    },
  },
});
//列表查询
const loadListData = async (data: any) => {
  if (queryItems.value.code.options.length == 0) {
    queryItems.value.code.options = (await api.getMetaDictList("index-error-code")).map((item) => ({
      label: `${item.name}(${item.code})`,
      value: item.code,
    }));
  }
  const result = await dataAssetApi.getFailDetailListPage(data);
  nextTick(async () => {
    failDetailCount.value = (await dataAssetApi.getFailDetailCount(data)).data;
    myTableRef.value.setTotal(failDetailCount.value);
  });
  return result.data;
};
const transformQuery = (data: any) => ({
  bucketCode: bucketInfo.value.bucketCode,
  executionId: executionInfo.value.executionId,
  ...data,
});
//转换接口返回的数据
const transformListData = (data: any) => {
  //取第一行作为样本
  rowExample.value = data[0] || {};
  return data.map((x: any) => {
    keys(x).forEach((key) => {
      if (key.endsWith("_ts")) {
        x[`${key}Render`] = timeC.format(x[key], "YYYY-MM-DD hh:mm:ss");
      }
    });
    x.codeRender = queryItems.value.code.options.find((item) => Number(item.value) === x.code)?.label;
    return x;
  });
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  exportExcel: (record: any) => {
    dataAssetApi.downloadFailDetail(bucketInfo.value.bucketCode, executionInfo.value.executionId, query.value.code).then((res) => {
      util.downloadFile(res, "失败数据.xlsx");
    });
  },
});
const loadList = () => myTableRef.value.loadData();
const openWindow = async (bi: any, ei: any) => {
  dialogVisible.value = true;
  bucketInfo.value = bi;
  executionInfo.value = ei;
  nextTick(() => {
    loadList();
  });
};
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss" scoped>
.header-back {
  @include model(32px, 32px);
  @include flexCenter();
  background: #f0f4fe;
  border-radius: 5px;
  margin-right: 12px;
  cursor: pointer;
}
.fail-detail-table {
  height: calc(100vh - 75px);
}
.pagination-info {
  font-size: 16px;
  font-weight: 700;
  margin-right: 15px;

  span + span {
    margin-left: 20px;
  }
}
</style>
<style lang="scss">
.fail-detail {
  .el-dialog__header {
    padding: 0 0 10px 0 !important;
  }
  .el-dialog__body {
    padding: 0 !important;
  }
}
</style>
