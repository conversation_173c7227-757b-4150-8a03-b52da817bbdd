<template>
  <my-drawer class="history-detail" v-model="dialogVisible" :title="dialogTitle" :show-confirm="false" :show-close="false" @close="handleClose" size="600">
    <div style="padding: 5px 20px 0 20px">
      <el-select
        v-if="!isDebug && !isAudit"
        v-model="taskType"
        placeholder="请选择任务类型"
        multiple
        @change="events.refresh"
        style="width: 330px; margin-right: 12px"
      >
        <el-option v-for="item in taskTypeList" :label="item.label" :value="item.value" />
      </el-select>
      <el-button class="refresh-btn" :icon="Refresh" @click="events.refresh" />
    </div>
    <div class="content-wrap">
      <template v-if="taskExecutionList.length > 0">
        <div v-for="(item, index) in taskExecutionList" class="item">
          <div class="item-index">{{ (page.page - 1) * page.size + index + 1 }}</div>
          <TaskItem :bucketInfo="bucketInfo" :executionInfo="item" @reload="loadListData" @fail-detail="failedDetail" :operationAuth="operationAuth" />
        </div>
      </template>
      <div v-else>
        <my-empty :size="120" />
      </div>
    </div>
    <div v-if="taskExecutionList.length > 0" class="pagination-wrap">
      <el-pagination
        background
        :current-page="page.page"
        :page-size="page.size"
        :total="page.total"
        layout="prev, pager, next"
        :pager-count="4"
        @current-change="events.handleCurrentChange"
        style="margin-right: 20px"
      />
    </div>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { dataC, timeC } from "turing-plugin";
import { copyText } from "@/utils/helpers";
import { cloneDeep } from "lodash";
import TaskItem from "../task/TaskItem.vue";
import * as dataAssetApi from "@/api/data-asset";
import { Refresh } from "@element-plus/icons-vue";
import useCtx from "@/hooks/useCtx";

const props = defineProps({
  operationAuth: { type: String, default: "" },
});

const emit = defineEmits(["fail-detail"]);

const { $app, proxy, $auth } = useCtx();

// Dialog相关
const dialogTitle = computed(
  () => `${bucketInfo.value.bucketName}(${bucketInfo.value.bucketCode})-${isDebug.value ? "调试历史" : isAudit.value ? "审核历史" : "任务历史"}`
);
const dialogVisible = ref(false);
const bucketInfo = ref({});
const taskExecutionList = ref([]);
const taskTypeList = computed(() => {
  if (isDebug.value) {
    return [];
  } else if (isAudit.value) {
    return [{ label: "审核任务", value: "3" }];
  } else {
    return [
      { label: "构建任务", value: "0" },
      { label: "同步任务", value: "1" },
      { label: "去重任务", value: "2" },
    ];
  }
});
const taskType = ref([]);
const isDebug = ref(false);
const isAudit = ref(false);

const page = reactive({
  page: 1,
  size: 10,
  total: 0,
});

//列表查询
const loadListData = () => {
  const params = {
    page: page.page,
    size: page.size,
    sort: "createdDate,desc",
    bucketCode: bucketInfo.value.bucketCode,
    debug: isDebug.value ? 1 : 0,
    type: taskType.value.join(","),
  };
  dataAssetApi.getTaskExecutionListPage(params).then((result) => {
    taskExecutionList.value = result.data.content;
    page.total = result.data.totalElements;
  });
};

// 方法：打开窗口
const openWindow = (info: any, type: string) => {
  dialogVisible.value = true;
  bucketInfo.value = info;
  isDebug.value = type === "debug";
  isAudit.value = type === "audit";
  taskType.value = isDebug.value ? [] : isAudit.value ? ["3"] : ["0", "1", "2"];
  events.handleCurrentChange(1);
};

// 事件处理
const events = {
  handleCurrentChange: (val) => {
    page.page = val;
    loadListData();
  },
  refresh: () => {
    loadListData();
  },
};

const failedDetail = (bucketInfo: any, executionInfo: any) => {
  emit("fail-detail", bucketInfo, executionInfo);
};

const handleClose = () => {
  taskExecutionList.value = [];
};

// 接口暴露
defineExpose({ openWindow });
</script>

<style lang="scss">
.history-detail {
  .el-drawer__body {
    padding: 0;
  }
  .content-wrap {
    height: calc(100% - 90px);
    overflow-y: auto;
    .item {
      position: relative;
      padding: 10px 20px 10px 30px;
      .item-index {
        position: absolute;
        height: 20px;
        width: 20px;
        left: 8px;
        top: 12px;
        font-family: fantasy;
        font-size: 20px;
        color: #c0c0c0;
      }
    }
  }
  .pagination-wrap {
    display: flex;
    float: right;
  }
  .icon-copy {
    color: $primary-color;
    margin-left: 5px;
    cursor: pointer;
  }
  .refresh-btn {
    padding: 8px;
  }
}
</style>
