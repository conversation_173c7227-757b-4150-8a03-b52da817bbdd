<template>
  <span v-if="dataC.isEmpty(taskInfo?.lastExecution)">-</span>
  <div v-else class="flex">
    <el-popover placement="right" :width="600">
      <template #reference>
        <status-dot v-if="!isRunning" :type="dotType" :name="dotName"></status-dot>
        <div v-if="isRunning" style="width: 200px">
          <el-progress :text-inside="true" :stroke-width="16" :percentage="percentage" status="primary" style="flex: 1"> </el-progress>
        </div>
      </template>
      <el-timeline>
        <el-timeline-item v-for="(item, index) in lastExecution" :key="index" type="primary" hide-timestamp>
          <TaskItem :bucketInfo="bucketInfo" :executionInfo="item" @reload="reload" @fail-detail="failedDetail" :operationAuth="operationAuth" />
        </el-timeline-item>
      </el-timeline>
    </el-popover>
    <template v-if="isRunning">
      <my-button link type="primary" @click="cancel" class="task-process-btn" :operationAuth="operationAuth"> 取消 </my-button>
      <el-dropdown trigger="click" style="padding: 0">
        <el-button link type="primary" size="small" :icon="ArrowDownBold" style="margin-left: 0" />
        <template #dropdown>
          <el-dropdown-item @click="forceFinish">强制完成</el-dropdown-item>
        </template>
      </el-dropdown>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as dataAssetApi from "@/api/data-asset";
import TaskItem from "../task/TaskItem.vue";
import { ArrowDownBold } from "@element-plus/icons-vue";

const props = defineProps({
  bucketInfo: { type: Object, required: true },
  taskInfo: { type: Object, required: true },
  operationAuth: { type: String, default: "" },
});

const emit = defineEmits(["reload", "fail-detail"]);

const { $app } = useCtx();

const TASK_STATUS = {
  "0": { type: "info", name: "已创建" },
  "1": { type: "primary", name: "运行中" },
  "2": { type: "success", name: "已完成" },
  "-1": { type: "warning", name: "已取消" },
  "-2": { type: "info", name: "已重置" },
  "-3": { type: "danger", name: "失败" },
  "20": { type: "success", name: "强制完成" },
};

const lastExecution = computed(() => props.taskInfo?.lastExecution || []);

const isRunning = computed(() => {
  return lastExecution.value.some((item) => item.status === 1);
});

//当前子任务的序号
const activeIndex = computed(() => {
  //为空返回-1
  if (dataC.isEmpty(lastExecution.value)) return -1;
  //返回第一个不是已结束的
  for (let i = 0; i < lastExecution.value.length; i++) {
    if (lastExecution.value[i].status != 2) {
      return i;
    }
  }
  //返回最后一个
  return lastExecution.value.length - 1;
});
//当前子任务
const activeTask = computed(() => {
  if (activeIndex.value == -1) {
    return undefined;
  }
  return lastExecution.value[activeIndex.value];
});

const dotType = computed(() => TASK_STATUS[activeTask.value.status]?.type || "info");
const dotName = computed(() => TASK_STATUS[activeTask.value.status]?.name || "未知状态");

const percentage = computed(() => {
  const done = Number(activeTask.value.done + activeTask.value.fail || 0);
  const total = Number(dataC.isEmpty(activeTask.value.total) || activeTask.value.total === 0 ? 100_000_000 : activeTask.value.total);

  if (total === 0) return 0;

  const result = (done / total) * 100;
  return Math.min(100, parseFloat(result.toFixed(2)));
});

const cancel = async () => {
  try {
    await $app.$confirm({ title: `【${props.bucketInfo.bucketName}】确定取消任务吗?` });
    await dataAssetApi.cancelTask(props.bucketInfo.bucketCode, activeTask.value.type);
    emit("reload");
    $app.$message.success("取消任务成功");
  } catch (error) {
    if (error) {
      $app.$message.error(error);
    }
  }
};

const forceFinish = async () => {
  try {
    await $app.$confirm({ title: `【${props.bucketInfo.bucketName}】确定强制完成任务吗?` });
    await dataAssetApi.forceFinishTask(props.bucketInfo.bucketCode, activeTask.value.type);
    emit("reload");
    $app.$message.success("强制完成任务成功");
  } catch (error) {
    if (error) {
      $app.$message.error(error);
    }
  }
};

const failedDetail = (bucketInfo: any, executionInfo: any) => {
  emit("fail-detail", bucketInfo, executionInfo);
};

const reload = () => {
  emit("reload");
};
</script>
<style lang="scss" scoped>
.task-process-btn {
  margin-left: 5px;
  width: 40px;
}
::v-deep {
  .el-button.is-link {
    vertical-align: baseline;
  }
}
</style>
