<template>
  <my-drawer class="time-task" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" confirmText="保存" size="800">
    <div v-if="showCronConfig">
      <h3 class="common-part-title">
        <el-switch v-model="cronEnabled" :active-value="true" :inactive-value="false" active-text="开启定时任务" />
      </h3>
    </div>
    <div v-show="showCronConfig && cronEnabled">
      <div style="margin-top: 10px">
        <span class="cron-tip">
          cron预览 : <span class="el-tag el-tag--primary el-tag--light">{{ cronTrim }}</span>
        </span>
      </div>
      <template v-if="cronValue !== ''">
        <noVue3Cron ref="cronRef" :cron-value="cronValue" i18n="cn"> </noVue3Cron>
      </template>
    </div>
    <el-divider v-if="showCronConfig" direction="horizontal" border-style="dashed" style="margin: 12px 0" />
    <div v-if="showAtuoDedupConfig">
      <h3 class="common-part-title">
        <el-switch v-model="dedupTriger.autoEnabled" :active-value="true" :inactive-value="false" active-text="自动构建时进行去重" />
      </h3>
    </div>
    <div v-if="showManualDedupConfig">
      <h3 class="common-part-title">
        <el-switch v-model="dedupTriger.manualEnabled" :active-value="true" :inactive-value="false" active-text="手动构建时进行去重" />
      </h3>
    </div>
    <div v-if="showAtuoDedupConfig || showManualDedupConfig">
      <DedupEdit ref="dedupEditRef" />
    </div>
    <template v-if="showManualDedupConfig" #extraOperate>
      <el-button type="primary" @click="handleConfirm(true)">保存并执行</el-button>
    </template>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as dataAssetApi from "@/api/data-asset";
import DedupEdit from "./DedupEdit.vue";
import { noVue3Cron } from "no-vue3-cron";
import "no-vue3-cron/lib/noVue3Cron.css";

const { $app, proxy, $router } = useCtx();
const { api } = useStore();

const props = defineProps({
  showCronConfig: { type: Boolean, default: false },
  showAtuoDedupConfig: { type: Boolean, default: false },
  showManualDedupConfig: { type: Boolean, default: false },
});

// 事件声明
const emit = defineEmits(["reload"]);

// Dialog相关
const dialogTitle = computed(() => `${bucketInfo.value.bucketName}(${bucketInfo.value.bucketCode})-${props.showCronConfig ? "定时配置" : "去重配置"}`);
const dialogVisible = ref(false);

const bucketInfo = ref({});
const cronRef = ref(null);
const cronEnabled = ref(false);
const cronValue = ref("");
const cronTrim = computed(() => {
  const val = cronRef.value?.getValue() || "0 0 0 * * ? *";
  return val.split(" ").splice(0, 6).join(" ");
});
const dedupTriger = ref({});
const dedupEditRef = ref();

// 方法：打开窗口
const openWindow = async (row: any) => {
  dialogVisible.value = true;
  bucketInfo.value = { bucketCode: row.code, bucketName: row.name };
  const res = await dataAssetApi.getBucketSchedule(bucketInfo.value.bucketCode);
  //定时任务-渲染表单(后台存的时六位的cron，但是这个组件时七位的cron)
  cronValue.value = res.cron ? `${res.cron} *` : "0 0 0 * * ? *";
  cronEnabled.value = !dataC.isEmpty(res.cron) ? true : false;
  //组件渲染成功后设置分钟选中(已通过css样式设置秒tab和年tab不可见)
  await nextTick();
  document.getElementById("tab-1")?.click();
  //自动去重-渲染表单
  dedupTriger.value =
    res.trigger && "null" != res.trigger ? JSON.parse(res.trigger) : { when: "done", event: "dedup", param: {}, autoEnabled: false, manualEnabled: false };
  dedupEditRef.value?.handleUpdate(cloneDeep(dedupTriger.value.param || {}));
};
// 方法：关闭窗口
const handleClose = () => {
  dialogVisible.value = false;
  cronValue.value = "";
  cronEnabled.value = false;
  dedupTriger.value.autoEnabled = false;
  dedupTriger.value.manualEnabled = false;
};
// 方法：提交表单
const handleConfirm = async (execute: boolean = false) => {
  //如果启用定时任务，需要先校验配置有效性
  if (cronEnabled.value) {
    const check = checkCronFrequency(cronTrim.value);
    if (check) {
      $app.$message.warning(check);
      return;
    }
  }
  //如果启用去重配置，需要先校验配置有效性
  if ((props.showAtuoDedupConfig && dedupTriger.value.autoEnabled) || (props.showManualDedupConfig && dedupTriger.value.manualEnabled)) {
    const triggerParam = dedupEditRef.value?.getFormData();
    if (!triggerParam) return;
    dedupTriger.value.param = triggerParam;
  }
  //保存定时任务配置
  try {
    await dataAssetApi.saveBucketSchedule(bucketInfo.value.bucketCode, cronEnabled.value ? cronTrim.value : "", dedupTriger.value);
    if (!execute) {
      $app.$message.success("保存成功");
      emit("reload");
      handleClose();
    }
  } catch (error) {
    $app.$message.warning("保存失败");
  }
  //执行去重任务
  if (execute) {
    try {
      await dataAssetApi.dedupRecord(bucketInfo.value.bucketCode, dedupTriger.value.param.script, dedupTriger.value.param.scriptParam);
      $app.$message.success("保存并执行成功");
      emit("reload");
      handleClose();
    } catch (error) {
      $app.$message.warning("执行失败");
    }
  }
};
function checkCronFrequency(cronExpression) {
  const parts = cronExpression.split(" ");
  // 检查秒级频率（第一位）
  if (parts[0] === "*") {
    return "秒级定时任务执行频率过高，可能影响系统性能";
  }
  // 检查分级频率（第二位）
  if (parts[1] === "*") {
    return "分级定时任务执行频率过高，可能影响系统性能";
  }
  // 其他检查...
  return null; // 无过高频率问题
}
//初始化
onMounted(async () => {});
// 接口暴露
defineExpose({ openWindow });
</script>

<style lang="scss" scoped>
.time-task {
  .cron-tip {
    color: red;
    margin-left: 10px;
  }
  .no-vue3-cron-div {
    margin-top: 10px;
    position: relative;
  }
}
:deep(#tab-0) {
  display: none;
}
:deep(#tab-5) {
  display: none;
}
:deep(.language) {
  display: none;
}
:deep(.bottom) {
  display: none !important;
}
:deep(.el-button--small) {
  display: none;
}
</style>
