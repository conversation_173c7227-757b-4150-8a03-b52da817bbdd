<template>
  <page-wrapper route-name="analysis-plan::details::">
    <div class="analysis-plan-container">
      <el-card
        class="info-card"
        :style="{ height: activeCollapse == 1 ? '170px' : '60px' }"
      >
        <el-collapse
          v-model="activeCollapse"
          @change="events.collapseChange"
          class="collapse"
        >
          <el-collapse-item :name="1">
            <template #title>
              <span style="font-size: 16px; font-weight: 700">基本信息</span>
            </template>
            <el-descriptions column="3">
              <el-descriptions-item
                label="计划名称 : "
                label-class-name="bold"
                >{{ `${baseInfo.name}` }}</el-descriptions-item
              >
              <el-descriptions-item label="分组 : " label-class-name="bold">{{
                baseInfo.groupName
              }}</el-descriptions-item>
              <el-descriptions-item
                label="doc条数 : "
                label-class-name="bold"
                >{{ baseInfo.total }}</el-descriptions-item
              >
              <el-descriptions-item
                label="归因模式 : "
                label-class-name="bold"
                >{{
                  baseInfo.mode == 0 ? "对标good" : "自研bad"
                }}</el-descriptions-item
              >
              <el-descriptions-item
                label="归因策略流程 : "
                label-class-name="bold"
                >{{ (modelValue.ascribeList[baseInfo.mode]||[]).find((item: any) => item.value == baseInfo.processId)?.label ||'无' }}</el-descriptions-item
              >
              <el-descriptions-item
                label="场景策略流程 : "
                label-class-name="bold"
                >{{ (modelValue.anasisList||[]).find((item: any) => item.value == baseInfo.sceneProcId)?.label ||'无' }}</el-descriptions-item
              >
              <el-descriptions-item
                :span="3"
                label="计划描述 : "
                label-class-name="bold"
                >{{ baseInfo.description||"无" }}</el-descriptions-item
              >
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <el-card
        class="table-card"
        :style="{
          height:
            activeCollapse == 1 ? 'calc(100vh - 320px)' : 'calc(100vh - 210px)',
        }"
      >
        <div class="el-descriptions">
          <div
            class="el-descriptions__header"
            :class="!isPlan ? 'no-bottom' : ''"
          >
            <div class="el-descriptions__title">
              <span>{{ isPlan ? "任务详情" : "结果详情" }}</span>
              &nbsp;
              <el-button link type="primary" @click="events.refreshSite">
                <el-icon size="18">
                  <Refresh />
                </el-icon>
              </el-button>
            </div>
            <my-button
              type="export"
              @click="events.exportExcel"
              style="margin-left: 20px"
              >导出</my-button
            >
          </div>
        </div>

        <div class="idx-db-statistic-site-table">
          <div class="flexBetweenStart" style="align-items: end" v-if="!isPlan">
            <el-tabs
              v-model="typeActive"
              @tab-change="handleTabChange"
              style="margin-bottom: 10px"
            >
              <el-tab-pane
                v-for="option in typeEnum"
                :label="`${option.label}${option.num}条`"
                :name="option.value"
              >
              </el-tab-pane>
            </el-tabs>
            <div style="margin-bottom: 20px">
              <span
                v-for="key in Object.keys(totalInfo || {})"
                class="inline-block name"
              >
                <span>{{ key }}: </span><span>{{ totalInfo[key] }}</span>
              </span>
            </div>
          </div>
          <myTable
            ref="myTableRef"
            :columns="columns"
            :tableData="tableData"
            :transformListData="transformListData"
            :defaultPageSizes="[100, 200]"
            :loadImmediately="true"
            :withOrder="withOrder"
          >
            <template #[slotName]="scope">
              <el-popover
                placement="right"
                :width="500"
                v-if="scope.row?.errorMsg"
              >
                {{ scope.row?.errorMsg }}
                <template #reference>
                  <text-button
                    type="primary"
                    :style="{ fontWeight: 550 }"
                    v-if="!isPlan"
                    @click="columns[0].customRender.click(scope.row)"
                  >
                    {{ scope.row?.[slotName] }}
                  </text-button>
                  <span v-else> {{ scope.row?.[slotName] }}</span>
                </template>
              </el-popover>
              <text-button
                type="primary"
                :style="{ fontWeight: 550 }"
                v-if="!isPlan"
                @click="columns[0].customRender.click(scope.row)"
              >
                {{ scope.row?.[slotName] }}
              </text-button>
              <span v-else>{{ scope.row?.[slotName] }}</span>
            </template>
          </myTable>
        </div>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as evaluationApi from "@/api/eval-evaluation";
import * as util from "@/utils/common";
import { computed } from "vue";
import {getTextLength} from '@/utils/common.ts'
import TextButton from "@/components/button/TextButton.vue";
const { $router, proxy, $app } = useCtx();
const { api } = useStore();
const props = defineProps(["transformListData"]);
import * as commonApi from "@/api/common";
const mode = $router.currentRoute.value.query.mode;
let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
let typeActive = ref("1");
const tableData = ref([]);
const baseInfo = ref({});
const routeName = "analysis-plan";
//事件声明
const emit = defineEmits(["preview-data"]);
const activeCollapse = ref([1]);
let totalInfo = ref({});
const modelValue = ref({
  ascribeList: [],
  anasisList: [],
});
const typeEnum = ref([
  // { value: "0", label: "全部", type: "total", num: 0 },
  { value: "1", label: "成功", type: "success", num: 0 },
  { value: "2", label: "失败", type: "fail", num: 0 },
  // ... 更多选项
]);
const withOrder = ref<Boolean>(false);
const columns = ref<any[]>([]);
let slotName = computed(() => {
  if (columns.value?.[0]) {
    return columns.value?.[0].prop;
  } else {
    return "";
  }
});
const isPlan = computed(() => {
  return mode == "plan";
});
const isGood = computed(() => {
  return mode == "good";
});
const isBad = computed(() => {
  return mode == "bad";
});
const handleTabChange = (paneName: string) => {
  loadListData();
};
//列表查询
const loadListData = async () => {

  
  return new Promise((resolve: any) => {
    let api = isPlan.value ? "getPlanDetailList" : "getPlanResultList";
    let evalResultObjects = {};
    //先将序号和动态列不显示
    withOrder.value = false;
    columns.value = [];
    //然后从后端读取表格内容
    evaluationApi[api](
      $app.$route.query.planId,
      isPlan.value ? null : typeActive.value
    ).then((result) => {
      if (!isPlan.value && result.content && result.content.length) {
        let column = [];
        //成功表头
        if (result.content?.[0]?.evalResult) {
          column = (result.content?.[0]?.evalResult || []).map((res: any) => ({
            prop: res.name,
            label: res.name,
            minWidth: getTextLength(res.name)*10+35,
          }));
          columns.value.push({
            prop: "",
            custom: "columns",
            label: isBad.value ? "BadCase问题定位环节" : "GoodCase问题定位环节",
            columns: column,
          });
        }
        //失败表头
        if (result.content?.[0]?.errorMsg) {
          columns.value.push({
            prop: "errorMsg",
            label: "错误原因",
            minWidth: 200,
          });
        }

        // 使用 map 方法来处理每个数据对象中的 evalResult 数组
        evalResultObjects = result.content.map((dataItem) => {
          return (dataItem.evalResult || []).reduce((acc, resultItem) => {
            acc[resultItem.name] = resultItem.value;
            return acc;
          }, {});
        });
      }
      let columns1: any = Object.keys(result.content?.[0]?.input || {}).map(
        (key: any) => {
          let obj = {
            prop: key,
            label: key,
            minWidth: 140,
          };
          if (
            typeActive.value == "1" &&
            ((isBad.value && key == "badUrl") ||
              (isGood.value && key == "goodUrl"))
          ) {
            obj = {
              ...obj,
              blod: true,
              custom: "link",  
              withCopy: true, 
              customRender: {
                click: (record: any) => {
                  $router.push({
                    name: `analysis-plan::details::position`,
                    query: {
                      metaLabel: [...metaLabel, "全链路"],
                      queryId: record.id,
                      url: record[key],
                      planId: $app.$route.query.planId,
                      mode,
                    },
                  });
                },
              },
            };
          }
          if (
            typeActive.value == "1" &&
            ((isBad.value && key == "badId") ||
            (isGood.value && key == "goodId"))
          ) {
            obj = {
              ...obj,
              blod: true,
              custom: "link",  
              withCopy: true, 
              customRender: {
                click: (record: any) => {
                  $router.push({
                    name: `analysis-plan::details::position`,
                    query: {
                      metaLabel: [...metaLabel, "全链路"],
                      queryId: record.id,
                      searchId: record[key],
                      planId: $app.$route.query.planId,
                      mode,
                    },
                  });
                },
              },
            };
          }
          return obj;
        }
      );
      columns.value = columns1.concat(columns.value);
      //返回数据,直接赋值会让vue响应不及时，这里采用先删再增的方法
      tableData.value.splice(0, tableData.value.length);
      tableData.value.push(
        ...result.content.map((item, index) => {
          if (evalResultObjects?.[index]) {
            return {
              ...item,
              ...item.input,
              ...evalResultObjects[index],
              searchId: item.input?.id,
            };
          } else {
            return {
              ...item,
              ...item.input,
              searchId: item.input?.id,
            };
          }
        })
      );
      //根据结果显示是否显示序号，当有数据时显示序号
      withOrder.value = tableData.value.length > 0;
      //渲染表格
      loadList();
    });
  });
};
const getBaseInfo = async () => {
  evaluationApi.getPlanInfo($app.$route.query.planId).then((res) => {
    baseInfo.value = res;
  });
};
const updateSingle = (id) => {
  evaluationApi.getPlanProcess(id).then((res) => {
    typeEnum.value.map((item) => {
      item.num = res[item.type];
    });
    totalInfo.value = res.execResult;
  });
};
//归因策略
const getAscribeList = async (type: any, name = "") => {
  const res = await evaluationApi.getStrategyVersion(type, { name });
  return res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
//场景策略
const getAnasisList = async (name = "") => {
  const res = await commonApi.getSceneVersion({ name });
  return res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
//初始化
onMounted(async () => {
  getBaseInfo();
  updateSingle($app.$route.query.planId);
  modelValue.value.ascribeList[0] = await getAscribeList(0);
  modelValue.value.ascribeList[1] = await getAscribeList(1);
  modelValue.value.anasisList = await getAnasisList();
  nextTick(() => {
    loadListData();
  });
});
//事件列表
const events = reactive({
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  refreshSite: () => {
    updateSingle($app.$route.query.planId);
    loadListData();
  },
  exportExcel: () => {
    if (isPlan.value) {
      evaluationApi
        .downloadPlanDetail($app.$route.query.planId)
        .then((res) =>
          util.downloadFile(
            res,
            `${baseInfo.value.name}统计${
              isPlan.value ? "详情" : "结果"
            }信息.xlsx`
          )
        );
    } else {
      evaluationApi
        .downloadPlanResult($app.$route.query.planId, typeActive.value)
        .then((res) =>
          util.downloadFile(
            res,
            `${baseInfo.value.name}统计${
              isPlan.value ? "详情" : "结果"
            }信息.xlsx`
          )
        );
    }
  },
});
const loadList = () => {
  proxy.$refs["myTableRef"]?.loadData();
};
//接口暴露
defineExpose({
  loadList,
  loadListData,
});
</script>
<style lang="scss">
.analysis-plan-container {
  padding: 10px;

  .info-card {
    .bold {
      font-weight: bold;
    }

    .collapse {
      border: none;
      .el-collapse-item__header {
        border: none;
        height: 23px;
        margin-bottom: 12px;
      }

      .el-collapse-item__wrap {
        border: none;
      }
    }

    .el-card__body {
      height: 100%;
      .el-collapse {
        height: 100%;
        .el-collapse-item {
          height: 100%;
          .el-collapse-item__wrap {
            height: 100%;
            .el-collapse-item__content {
              height: 100%;
              .el-descriptions {
                height: 100%;
                .el-descriptions__body {
                  height: 100%;
                  overflow-y: auto;
                }
              }
            }
          }
        }
      }
    }
  }

  .table-card {
    margin-top: 10px;

    .el-card__body {
      height: 100%;
    }

    .no-bottom {
      margin-bottom: 0;
    }
  }
}
.idx-db-statistic-site-table {
  height: calc(100% - 60px);

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
::v-deep(.el-radio-group) {
  margin-bottom: 14px;
}
.total-info,
.inline-block {
  display: inline-block;
}
.name + .name {
  margin-left: 15px;
}
</style>