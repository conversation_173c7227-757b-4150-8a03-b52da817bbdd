<template>
  <div class="annotation-tree-component">
    <!-- 空状态提示 -->
    <div v-if="!annotationData || annotationData.length === 0" class="empty-state">
      <p>暂无标注数据</p>
    </div>

    <!-- 水平布局展示不同维度 -->
    <div v-else class="dimensions-container">
      <div v-for="dimension in annotationData" :key="dimension.code" class="dimension-wrapper">
        <!-- 维度标题 -->
        <div class="dimension-header">
          <span class="dimension-name">{{ dimension.name }}</span>
          <span v-if="dimension.required" class="required-mark">*</span>
        </div>

        <!-- 树形结构 -->
        <div class="dimension-tree">
          <el-tree :data="dimension.children" :props="treeProps" :default-expand-all="true" :show-checkbox="false"
            node-key="code" class="annotation-tree">
            <template #default="{ data }">
              <div class="tree-node-content" :class="`level-${data.level}`">
                <!-- 根据optType和层级显示不同的选择控件 -->
                <div class="node-selector">
                  <!-- Level 0 维度节点 - 只显示名称 -->
                  <template v-if="data.level === 0">
                    <span class="node-label">{{ data.name }}</span>
                  </template>

                  <!-- Level 1-3 选项节点 - 根据父级optType显示单选或多选 -->
                  <template v-else>
                    <!-- 单选模式 -->
                    <template v-if="getParentOptType(data) === 1">
                      <el-checkbox v-model="data.value" @change="handleRadioChange(data, $event)" class="node-radio">
                        {{ data.name }}
                      </el-checkbox>
                    </template>

                    <!-- 多选模式 -->
                    <template v-else>
                      <el-checkbox v-model="data.value" @change="handleCheckboxChange(data, $event)"
                        class="node-checkbox">
                        {{ data.name }}
                      </el-checkbox>
                    </template>
                  </template>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { cloneDeep } from 'lodash'

// 定义props
const props = defineProps<{
  modelValue: any[]
}>()

// 定义emits
const emits = defineEmits<{
  'update:modelValue': [value: any[]]
}>()

// 响应式数据
const annotationData = ref<any[]>([])

// 树组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 初始化数据
const initData = () => {
  // 处理空值情况，避免 map 方法报错
  if (!props.modelValue || !Array.isArray(props.modelValue)) {
    annotationData.value = []
    return
  }

  annotationData.value = cloneDeep(props.modelValue).map(item => {
    // 统一使用boolean值表示是否选中
    item.value = item.value === true || item.value === false ? item.value : false

    // 递归初始化子选项
    initChildrenValues(item)
    return item
  })
}

// 初始化子选项的值
const initChildrenValues = (node: any) => {
  if (node && node.children && Array.isArray(node.children) && node.children.length > 0) {
    node.children.forEach((child: any) => {
      if (child) {
        // 统一使用boolean值表示是否选中
        child.value = child.value === true || child.value === false ? child.value : false
        initChildrenValues(child)
      }
    })
  }
}

// 获取父级的optType
const getParentOptType = (node: any): number => {
  if (!node || !annotationData.value || !Array.isArray(annotationData.value)) {
    return 1
  }

  // 查找父级节点
  const findParent = (data: any[], target: any): any => {
    if (!data || !Array.isArray(data)) return null

    for (const item of data) {
      if (item && item.children && Array.isArray(item.children) && item.children.includes(target)) {
        return item
      }
      if (item && item.children && Array.isArray(item.children)) {
        const parent = findParent(item.children, target)
        if (parent) return parent
      }
    }
    return null
  }

  const parent = findParent(annotationData.value, node)
  return parent ? parent.optType : 1
}

// 处理单选变化
const handleRadioChange = (node: any, value: any) => {
  console.log(`单选变化 - 节点: ${node.name}, 值: ${value}`)

  if (value) {
    // 选中当前节点
    node.value = true

    // 级联选中所有父节点
    cascadeSelectParents(node)

    // 取消同级其他节点的选中状态
    const parent = findParentNode(node)
    if (parent && parent.children) {
      parent.children.forEach((sibling: any) => {
        if (sibling.code !== node.code) {
          sibling.value = false
          // 清空兄弟节点的子选项
          clearChildrenValues(sibling)
        }
      })
    }
  } else {
    node.value = false
    // 清空当前节点的子选项
    clearChildrenValues(node)
    const parent = findParentNode(node)
    if (parent.level == 0) {
      parent.value = false
    }
  }

  emitUpdate()
}

// 处理多选变化
const handleCheckboxChange = (node: any, value: any) => {
  console.log(`多选变化 - 节点: ${node.name}, 值: ${value}`)

  node.value = value

  if (value) {
    // 如果选中，级联选中所有父节点
    cascadeSelectParents(node)
  } else {
    // 如果取消选中，清空子选项
    clearChildrenValues(node)
    const parent = findParentNode(node)
    //当父节点的所有子节点都取消选中，父节点也取消选中
    if (parent && parent.children && parent.level == 0) {
      const allChildrenUnselected = parent.children.every((child: any) => !child.value)
      if (allChildrenUnselected) {
        parent.value = false
      }
    }

  }

  emitUpdate()
}

// 查找父节点
const findParentNode = (targetNode: any): any => {
  if (!targetNode || !annotationData.value || !Array.isArray(annotationData.value)) {
    return null
  }

  const findParent = (data: any[], target: any): any => {
    if (!data || !Array.isArray(data)) return null

    for (const item of data) {
      if (item && item.children && Array.isArray(item.children) && item.children.includes(target)) {
        return item
      }
      if (item && item.children && Array.isArray(item.children)) {
        const parent = findParent(item.children, target)
        if (parent) return parent
      }
    }
    return null
  }

  return findParent(annotationData.value, targetNode)
}

// 级联选中所有父节点
const cascadeSelectParents = (node: any) => {
  console.log(`级联选中父节点 - 当前节点: ${node.name}`)

  const parent = findParentNode(node)
  if (parent) {
    console.log(`找到父节点: ${parent.name}, 当前状态: ${parent.value}`)

    // 选中父节点
    parent.value = true

    // 如果父节点是单选模式，需要取消同级其他节点的选中状态
    const parentOptType = getParentOptType(parent)
    if (parentOptType === 1) {
      const grandParent = findParentNode(parent)
      if (grandParent && grandParent.children) {
        grandParent.children.forEach((sibling: any) => {
          if (sibling.code !== parent.code) {
            sibling.value = false
            clearChildrenValues(sibling)
          }
        })
      }
    }

    // 递归向上级联选中
    cascadeSelectParents(parent)
  }
}

// 清空子选项的值
const clearChildrenValues = (node: any) => {
  if (node && node.children && Array.isArray(node.children) && node.children.length > 0) {
    node.children.forEach((child: any) => {
      if (child) {
        child.value = false
        clearChildrenValues(child)
      }
    })
  }
}

// 发送更新事件
const emitUpdate = () => {
  const valueToEmit = annotationData.value && Array.isArray(annotationData.value)
    ? cloneDeep(annotationData.value)
    : []
  emits('update:modelValue', valueToEmit)
}

// 监听props变化
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true, deep: true })

onMounted(() => {
  // 页面渲染好后默认调用@update:modelValue回调，更新markindex组件中的标注值
  // 解决问题：当用户在markindex页面不点击标注内容，markindex中的标注值为空，此时点击保存会导致错误结果
  emitUpdate();
})
</script>

<style lang="scss" scoped>
.annotation-tree-component {
  width: 100%;

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;
    font-size: 14px;

    p {
      margin: 0;
    }
  }

  .dimensions-container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 6px;
    width: fit-content;
    min-width: 100%;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .dimension-wrapper {
      flex: 0 0 auto;
      width: fit-content;
      min-width: 180px;
      max-width: 280px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 10px;
      background: #fafafa;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

      .dimension-header {
        margin-bottom: 8px;
        padding: 6px 8px;
        border-bottom: 1px solid #409eff;
        background: linear-gradient(90deg, #409eff, #67c23a);
        border-radius: 3px 3px 0 0;
        margin: -10px -10px 8px -10px;

        .dimension-name {
          font-weight: 500;
          font-size: 13px;
          color: white;
          text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        }

        .required-mark {
          color: #ffeb3b;
          margin-left: 3px;
          font-weight: bold;
          font-size: 12px;
        }
      }

      .dimension-tree {
        width: fit-content;
        min-width: 100%;

        .annotation-tree {
          background: transparent;
          width: fit-content;
          min-width: 100%;

          :deep(.el-tree-node) {
            width: fit-content;
            min-width: 100%;

            .el-tree-node__content {
              height: auto;
              padding: 2px 0;
              background: transparent;
              width: fit-content;
              min-width: 100%;

              &:hover {
                background: rgba(64, 158, 255, 0.08);
              }
            }

            .el-tree-node__expand-icon {
              display: none;
            }

            .el-tree-node__children {
              padding-left: 1em;
              width: fit-content;
            }
          }

          .tree-node-content {
            width: fit-content;
            min-width: 100%;
            display: inline-block;

            &.level-0 {
              .node-label {
                font-weight: 500;
                color: #409eff;
                font-size: 12px;
                white-space: nowrap;
              }
            }

            &.level-1 {
              .node-selector {
                margin-left: 0.8em;
                padding: 2px 6px;
                border-left: 2px solid #409eff;
                background: rgba(64, 158, 255, 0.06);
                border-radius: 0 3px 3px 0;
                width: fit-content;
                display: inline-block;
              }
            }

            &.level-2 {
              .node-selector {
                margin-left: 1.6em;
                padding: 2px 6px;
                border-left: 2px solid #67c23a;
                background: rgba(103, 194, 58, 0.06);
                border-radius: 0 3px 3px 0;
                width: fit-content;
                display: inline-block;
              }
            }

            &.level-3 {
              .node-selector {
                margin-left: 2.4em;
                padding: 2px 6px;
                border-left: 2px solid #e6a23c;
                background: rgba(230, 162, 60, 0.06);
                border-radius: 0 3px 3px 0;
                width: fit-content;
                display: inline-block;
              }
            }

            .node-selector {
              padding: 1px 3px;
              border-radius: 2px;
              width: fit-content;

              .node-radio,
              .node-checkbox {
                margin: 0;
                white-space: nowrap;
                display: inline-block;

                :deep(.el-radio__label),
                :deep(.el-checkbox__label) {
                  font-size: 12px;
                  line-height: 1.3;
                  padding-left: 4px;
                  white-space: nowrap;
                }

                :deep(.el-radio__input),
                :deep(.el-checkbox__input) {

                  .el-radio__inner,
                  .el-checkbox__inner {
                    width: 12px;
                    height: 12px;
                  }

                  .el-radio__inner::after {
                    width: 4px;
                    height: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1000px) {
    .dimensions-container {
      flex-wrap: wrap;
      gap: 8px;
      width: 100%;

      .dimension-wrapper {
        flex: 0 0 auto;
        width: fit-content;
        min-width: 180px;
        max-width: 280px;
        padding: 8px;
      }
    }
  }

  @media (max-width: 600px) {
    .dimensions-container {
      flex-direction: column;
      gap: 6px;
      width: 100%;

      .dimension-wrapper {
        flex: none;
        width: 100%;
        min-width: auto;
        max-width: none;
        padding: 6px;
      }
    }
  }
}
</style>
