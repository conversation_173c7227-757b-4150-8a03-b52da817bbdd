<template>
  <div v-if="showStrategy" class="mark-strategy">
    <el-dialog title="全链路" v-model="allChainVisible" fullscreen class="position-dialog">
      <template #header> <el-button type="primary" @click="allChainVisible = false"
          :icon="Back">返回</el-button><Strong></Strong> </template>
      <MarkPosition ref="markPositionRef"></MarkPosition>
    </el-dialog>
    <el-card>
      <template #header>
        <div class="flexBetween">
          <div class="flex">
            <span class="info blue">{{ sceneStrategy }}</span>
            <el-dropdown v-if="strategyList.length > 1" placement="bottom" @command="events.changeStrategy">
              <el-button link :icon="Switch"> </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="(item, index) in strategyList" :command="item.id"
                    :disabled="strategyId == item.id">
                    {{ getSceneStrategy(item) }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <router-link v-if="strategyInfo.ascribeMode == 1" :to="allChainTo" style="margin-left: 10px">
            <el-button size="small" @click="events.allChain"
              :disabled="dataC.isEmpty(categoryId) || dataC.isEmpty(markRecordId)">查看全链路</el-button>
          </router-link>
        </div>
      </template>
      <div>
        <span class="info">
          运行环境：<span class="blue">{{ regionName }}</span>
        </span>
      </div>
      <div>
        <span class="info">
          归因模式：<span class="blue">{{ ascribeMode }}</span>
        </span>
      </div>
      <div>
        <span class="info">
          归因策略：<span class="blue">{{ ascribeStrategy }}</span> 
      <el-popover placement="right" width="auto" trigger="click" v-if="flowParamObj.inputArgs?.length > 0">
        <template #reference>
          <el-icon class="icon-tip">
            <Notification />
          </el-icon>
        </template>
        <div>
          <FlowParam :key="flowParamObj.paramUseKey" :inputArgs="flowParamObj.inputArgs" style="width: 100%">
          </FlowParam>

        </div>
      </el-popover>
        </span>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, onMounted, nextTick } from "vue";
import { Switch } from "@element-plus/icons-vue";
import { keys, cloneDeep, assign } from "lodash";
import { dataC } from "turing-plugin";
import MarkPosition from "./MarkPosition.vue";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
import { Back } from "@element-plus/icons-vue";
import FlowParam from "./FlowParam.vue";
import * as markApi from "@/api/eval-mark";
import { useVModel } from "@vueuse/core";
const { $app, $router, proxy } = useCtx();

const emits = defineEmits(["update:modelValue","update:flowParamObj"]);

const props = defineProps({
  modelValue: { type: String },
  strategyList: { type: Array as any, default: [] },
  targetId: {type: String},
  categoryId: { type: String },
  markRecordId: { type: String },
  regionList: { type: Array as any, default: [] },
  sceneList: { type: Array as any, default: [] },
  ascribeList: { type: Array as any, default: [] },
});
const flowParamObj = ref({
  paramUseKey:0,
  inputArgs:[]
});
//全链路显示
const allChainVisible = ref(false);

//策略ID
const strategyId = ref(props.modelValue);
//选中的策略信息
const strategyInfo = computed(() => {
  return dataC.getItemByValue(props.strategyList, strategyId.value, "id");
});
// 监听 modelValue, 对象的变化
watch(
  () => props.modelValue,
  (newVal) => {
    strategyId.value = newVal;
    flowParamObj.value = {
      paramUseKey:strategyInfo.value.paramUseKey,
      inputArgs:strategyInfo.value.inputArgs,
    }
  },
  {
    immediate: true,
  }
);
watch(flowParamObj,(newVal,oldVal)=>{
  emits("update:flowParamObj",newVal,strategyId.value);
},
{
  immediate:true,
  deep:true
}
);
//是否显示当前卡片
const showStrategy = computed(() => {
  return !dataC.isEmpty(props.strategyList);
});

//区域名称
const regionName = computed(() => {
  return dataC.getItemByValue(props.regionList, strategyInfo.value.regionCode, "code")["name"];
});

const getSceneStrategy = (strategy: any) => {
  if (strategy.offline) {
    return strategy.compName;
  } else {
    const info = dataC.getItemByValue(props.sceneList, strategy.processId, "processId");
    return `${info.name}(v${util.padNumberToDigits(info.version, 3)})`;
  }
};
//场景策略
const sceneStrategy = computed(() => {
  return getSceneStrategy(strategyInfo.value);
});

//归因模式
const ascribeMode = computed(() => {
  const dict = ["对标goodcase分析", "自研badcase分析"];
  return dict[strategyInfo.value.ascribeMode];
});

//归因策略
const ascribeStrategy = computed(() => {
  const info = dataC.getItemByValue(props.ascribeList, strategyInfo.value.ascribeProcessId, "processId");
  return info.name ? `${info.name}(v${util.padNumberToDigits(info.version, 3)})` : "暂无";
});

//获取全链路跳转的query参数
const getAllChainToQuery = () => {
  const oldQuery = $router.currentRoute.value.query;
  let newQuery = {};
  if (oldQuery.markMode == "mission") {
    newQuery = assign(newQuery, {
      markMode: "mission",
      missionId: oldQuery.missionId,
      group: oldQuery.group,
      targetId: oldQuery.targetId,
    });
  }
  newQuery = assign(newQuery, {
    mode: "ceping",
    categoryId: props.categoryId,
    markRecordId: props.markRecordId,
    strategyId: strategyId.value,
    targetId: props.targetId,
  });
  console.log("getAllChainToQuery:", newQuery);
  return newQuery;
};

//获取全链路跳转url地址
const allChainTo = computed(() => {
  const query = getAllChainToQuery();
  const routerQuery = new URLSearchParams();
  keys(query).forEach((key) => {
    routerQuery.append(key, String(query[key]));
  });
  return `/mark-index/position?${routerQuery.toString()}`;
});

const events = reactive({
  allChain: async () => {
    //阻止原生路由跳转事件
    event.preventDefault();
    //修改当前路由
    await $router.push({ name: `mark-index`, query: getAllChainToQuery() });
    //打开一个全屏dialog
    allChainVisible.value = true;
    //刷新全链路
    nextTick(() => {
      console.log("mark-strategy nextTick");
      proxy.$refs.markPositionRef.getTraceinfo();
    });
  },
  changeStrategy: (command: String) => {
    strategyId.value = command;
    emits("update:modelValue", command);
  },
});

</script>

<style lang="scss" scoped>
.mark-strategy {
  ::v-deep .el-card__header {
    background-color: #f2f2f2;
    padding: 7px 12px;
  }

  ::v-deep .el-card__body {
    background-color: #f2f2f2;
    padding: 7px 12px;
  }

  .info {
    white-space: nowrap;
  }

  .blue {
    color: #177bf8;
  }
}
</style>