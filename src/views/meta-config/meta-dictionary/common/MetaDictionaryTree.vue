<template>
  <el-card class="left-card">
    <div class="tab" v-if="type=='dict'">
      <el-tabs v-model="localAreaType" @tab-change="handleAreaTypeChange">
        <el-tab-pane
          :label="item.label"
          :name="item.value"
          v-for="item in configJs.areaTypeOptions"
        >
        </el-tab-pane>
      </el-tabs>
    </div>
    <div>
      <div class="title">
        <span>{{ title }}</span
        ><span v-if="type == 'dict'">
          <my-button link @click="events.export(data)"
            ><el-icon><Download /></el-icon
          ></my-button>
          <my-button
            link
            @click="events.import(data)"
            :operationAuth="operationAuth"
            ><el-icon><Upload /></el-icon></my-button
        ></span>
      </div>
      <el-input
        v-model="filterText"
        placeholder="请输入名称"
        @keyup.enter="emits('updateTree')"
      >
        <!-- v-if="treeData && treeData.length" -->
        <template #append>
          <el-button :icon="Search" @click="emits('updateTree')" size="small" />
        </template>
      </el-input>
    </div>
    <div class="layout-tab-filter" v-if="localAreaType!='1'">
      <span class="sub-title">所属区域</span>
      <el-select
        v-model="area"
        placeholder="请选择所属区域"
        style="width: 150px"
        @change="handleAreaChange"
      >
        <el-option
          v-for="item in areaOptions"
          :key="item.code"
          :label="item.name"
          :value="item.code"
        />
      </el-select>
    </div>
    <el-tree
      ref="treeRef"
      style="max-width: 600px"
      :allow-drop="allowDrop"
      :expand-on-click-node="false"
      @node-drop="handleDrop"
      :data="treeData"
      node-key="id"
      :draggable="type == 'word'"
      :current-node-key="currentKey"
      :default-expanded-keys="expandArr"
      :props="defaultProps"
      @node-expand="handleNodeExpand"
      :highlight-current="true"
      v-if="treeData && treeData.length"
    >
      <template #default="{ node, data }">
        <span
          :class="{ 'custom-tree-node': true, 'expire-node': !data.enabled }"
        >
          <span class="text" :title="node.label + '(' + data.code + ')'">
            <span
              :class="{
                status: true,
                active: data.enabled,
                error: !data.enabled,
              }"
            ></span>
            <el-icon
              class="icon-copy"
              @click="copyText(data.code, '已复制！')"
              title="复制code"
              ><CopyDocument
            /></el-icon>
            <span v-if="node.level === 1">&nbsp;{{ data.index + 1 }}.</span>
            &nbsp;{{ node.label }}({{ data.code }})
          </span>
          <el-popover
            placement="bottom"
            title=""
            :width="200"
            trigger="click"
            popper-class="operate-popover"
          >
            <template #reference>
              <el-icon class="icon-more"><MoreFilled /></el-icon>
            </template>
            <template #default>
              <div class="items">
                <my-button
                  link
                  @click="events.add(data.parentId)"
                  :operationAuth="operationAuth"
                  >新增同级菜单</my-button
                >
                <my-button
                  v-if="
                    (data.parentId == '0' && type == 'word') || type == 'dict'
                  "
                  @click="events.add(data.id)"
                  link
                  :operationAuth="operationAuth"
                  >新增子菜单</my-button
                >
                <my-button
                  link
                  :operationAuth="operationAuth"
                  @click="events.edit(data)"
                  >编辑</my-button
                >
                <my-button
                  link
                  :operationAuth="operationAuth"
                  @click="events.delete(data)"
                  >删除</my-button
                >
                <my-button
                  link
                  v-if="type == 'word'"
                  @click="events.export(data)"
                  >导出</my-button
                >
                <my-button
                  link
                  :operationAuth="operationAuth"
                  v-if="type == 'word'"
                  @click="events.import(data)"
                  >导入</my-button
                >
                <my-button
                  link
                  @click="events.enable(data)"
                  :operationAuth="operationAuth"
                  >{{ data.enabled ? "停用" : "启用" }}</my-button
                >

                <el-popover
                  class="box-item"
                  title=""
                  placement="right-start"
                  popper-class="operate-popover"
                  v-if="
                    ((showAreaType && localAreaType == 2) ||
                      (type == 'word' && areaInfo.envType == 1)) &&
                    (!data.children || !data.children.length)
                  "
                >
                  <div v-for="item in areaOptions" class="items">
                    <my-button
                      link
                      :operationAuth="operationAuth"
                      v-if="item?.envType == 2"
                      @click="events.sync(data, item.code)"
                      >{{ item.name }}
                    </my-button>
                  </div>
                  <template #reference>
                    <my-button link :operationAuth="operationAuth"
                      >同步
                    </my-button>
                  </template>
                </el-popover>
              </div>
            </template>
          </el-popover>
        </span>
      </template>
    </el-tree>
    <el-button type="primary" @click="events.add()" v-else>新增</el-button>
    <AddTreeDialog
      ref="addRef"
      @reload="loadList"
      :treeData="treeData"
      :treeNode="getCurrentNode()"
      :type="type"
      :showAreaType="showAreaType"
    />
    <ImportDialog
      ref="importRef"
      @reload="loadList"
      :treeData="treeData"
      :treeNode="getCurrentNode()"
      @reloadTable="loadTableList"
      :type="type"
    />
  </el-card>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, defineEmits, onMounted } from "vue";
import { assign, pick, keys } from "lodash";
import { copyText, getText } from "@/utils/helpers";
import { ElTree } from "element-plus";
import AddTreeDialog from "./addTree.vue";
import { Search } from "@element-plus/icons-vue";
import ImportDialog from "./import.vue";
import useStore from "@/store";
import useCtx from "@/hooks/useCtx";
import { downloadFile, findNodeById } from "@/utils/common.ts";
import * as metaWordApi from "@/api/meta-word";
import * as configJs from "./index";
import type {
  AllowDropType,
  NodeDropType,
} from "element-plus/es/components/tree/src/tree.type";
import { computed } from "vue";
import { useVModel } from "@vueuse/core";
const { $app, proxy } = useCtx();
const props = defineProps({
  treeData: { type: Array, default: 200 },
  title: { type: String },
  type: { type: String },
  operationAuth: { type: String },
  areaType: { type: String },
  areaInfo: { type: Object, default: {} },
  showAreaType: { type: Boolean, default: false },
});

const { word } = useStore();

const defaultForm = {
  parentId: 0,
  name: "",
  code: "",
  enabled: "",
};
const emits = defineEmits(["updateTree", "updateTable", "updateAreaType"]);
const localAreaType = useVModel(props, "areaType", emits);
const currentKey = ref("");
const dialogVisible = ref(false);
const areaOptions = ref<any>([]);
const area = ref("");
const filterText = ref("");
const expandArr = ref([]);
const treeRef = ref<InstanceType<typeof ElTree>>();

const data: any[] = [];

const defaultProps = {
  children: "children",
  label: "name",
};
const setCurrentKey = (key: any) => {
  if(!key){
    return
  }
  nextTick(() => {
    treeRef.value.setCurrentKey(key);
  });
};
const getCurrentNode = () => {
  return treeRef.value?.getCurrentNode();
};
const handleNodeExpand = (data: any, node: any, el: any) => {
  const expand: boolean = node.expand;
  if (expand) {
    expandArr.value.push(data.id);
  } else {
    expandArr.value.splice(expandArr.value.indexOf(node.id), 1);
  }
};
//事件列表
const events = reactive({
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  add: (parentId?: any) => {
    const params = { parentId: parentId || 0 };
    if (props.showAreaType) {
      params.type = props.areaType;
    }
    proxy.$refs.addRef?.openDialog("add", params);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        metaWordApi.deleteTreeNode(record.id, word.area + "/").then(() => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
  enable: (record: any) => {
    const params = pick(
      { ...record, enabled: !record.enabled },
      keys(assign({}, defaultForm))
    );
    if (props.showAreaType) {
      params.type = props.areaType;
    }
    metaWordApi.editTree(record.id, params, word.area + "/").then(() => {
      loadList(record);
      $app.$message.success(
        `${!record.enabled ? "启用" : "停用"} ${record.name} 成功`
      );
    });
  },
  export: (record: any) => {
    if (props.type == "word") {
      metaWordApi.exportDic({ nodeId: record.id }, word.area).then((res) => {
        // 使用例子
        downloadFile(res, record.name + ".xls");
      });
    } else {
      metaWordApi.exportWord({ nodeId: record.id }, word.area, localAreaType.value).then((res) => {
        // 使用例子
        downloadFile(res, "字典.xls");
      });
    }
  },
  import: (parentId: any, type?: string) => {
    proxy.$refs.importRef?.openDialog(
      "import",
      { parentId: parentId || 0 },
      type
    );
  },
  sync: (record: any, targetRegion: any) => {
    metaWordApi
      .syncTree(record.id, targetRegion, word.area + "/", props.type)
      .then(() => {
        $app.$message.success(`同步成功`);
      });
  },
});
const allowDrop = (draggingNode: any, dropNode: any, type: AllowDropType) => {
  if (draggingNode.level === dropNode?.level) {
    if (draggingNode.data.parentId === dropNode.data.parentId) {
      return type === "prev" || type === "next";
    }
  } else {
    return false;
  }
};
const handleDrop = (draggingNode: any) => {
  const node = findNodeById(
    [{ id: "0", children: props.treeData }],
    draggingNode.data.parentId
  );
  const ids = node.children.map((item: any) => item.id);

  const params = { parentId: draggingNode.data.parentId, ids };
  metaWordApi.sortTree(params, word.area).then(() => {
    emits("updateTree", draggingNode.data.id);
    $app.$message.success(`调整顺序成功`);
  });
};
const loadList = (node?: any) => {
  emits("updateTree", node?.id);
};
const handleAreaChange = async (val: string) => {
  word.areaChange(val);
  emits("updateTree");
};
const handleAreaTypeChange = (val) => {
  emits("updateAreaType", val);

  emits("updateTree");
};
const loadTableList = () => {
  emits("updateTable");
};
onMounted(async () => {
  const res: any = await word.getAreaList();
  console.log(props.type);

  // if (props.type == "dict") {
  //   areaOptions.value =
  //     res.filter((item: any) => item.envType == 1) || res?.[0];
  // } else {
  //   areaOptions.value = res;
  // }
  areaOptions.value = res;
  area.value = word.area;
  nextTick(() => {
    emits("updateTree");
  });
});
defineExpose({
  filterText,
  setCurrentKey,
  getCurrentNode,
  areaOptions,
});
</script>

<style lang="scss" scoped>
.tab {
  position: absolute;
  top: -46px;
  left: 100px;
}
.icon-copy {
  color: $primary-color;
  margin-left: 12px;
  cursor: pointer;
  vertical-align: middle;
}
.icon-more {
  height: 100%;
}
.left-card {
  width: 375px;
  flex-shrink: 0;
  .title {
    font-weight: 550;
    display: flex;
    justify-content: space-between;
    i {
      margin-left: 5px;
    }
  }
  .layout-tab-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    .sub-title {
      margin-right: 8px;
    }
  }
  .custom-tree-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0 3px;
    .status {
      display: inline-block;
      height: 8px;
      width: 8px;
      border-radius: 8px;
      vertical-align: middle;
      margin-right: 2px;
      &.active {
        background-color: var(--el-color-primary);
      }
      &.error {
        background-color: var(--el-color-danger);
      }
    }
    .text {
      display: inline-block;
      align-content: center;
      @include no-wrap();
      width: 280px;
      word-break: break-all;
    }
  }
  .expire-node {
    background-color: var(--el-fill-color-light);
  }
  ::v-deep {
    .el-input {
      margin: 12px 0 10px 0;
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep(.el-button + .el-button) {
  margin-left: 0;
}
.operate-popover {
  .items {
    ::v-deep(.el-button) {
      display: block;
      padding: 5px;
      width: 100%;
      text-align: left;
      cursor: pointer;
      &:hover {
        background: var(--el-color-primary-light-9);
      }
    }
  }
}
</style>
