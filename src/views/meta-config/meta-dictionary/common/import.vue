<template>
  <my-drawer
    class="mock-add"
    v-model="dialogVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <my-form
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems"
      @submit="submit"
    >
      <template #file>
        <my-upload
          ref="uploadRef"
          maxSize="10M"
          accept=".xls,.xlsx"
          v-model="ruleForm.file"
          drag
          drag-style
        >
          <my-button type="primary" plain>上传文件</my-button>
        </my-upload>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import * as metaWordApi from "@/api/meta-word";

import type { FormRules } from "element-plus";
import { NAME_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import useStore from "@/store";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const { t } = useI18n();
const { word } = useStore();

const props = defineProps({
  treeNode: { type: Object, default: {} },
  type: { type: String },
});
const dialogTitle = computed(() => {
  return formType.value === "add" ? t("btn.new") : t("btn.edit");
});
const uploadRef = ref()

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
// 表单相关
const formType = ref<string>("add");

const formRef = ref<any>(null);
const defaultForm = {
  nodeId: undefined,
  file: null,
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  word: [
    {
      required: true,
      trigger: "blur",
      message: "请输入query",
    },
  ],
  relationWords: [{ required: true, message: "请输入", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
    file: { label: "上传文件", type: "slot", slotName: "file" },
  });

const openDialog = async () => {
  // 1.打开弹窗
  dialogVisible.value = true;
  // 3. 回显相关的操作
  nextTick(() => {
    uploadRef.value.clearFiles()
    ruleForm.value = { nodeId: props.treeNode.id, file: null };
  });
};
const emits = defineEmits(["reloadTable"]);
const submit = (form: any) => {
  if (props.type == "word") {
    metaWordApi.importDic(form, word.area, props.type).then(() => {
      $app.$message.success("导入成功");
      emits("reloadTable");
      handleClose();
    });
  } else {
    metaWordApi.importWord(form, word.area).then(() => {
      $app.$message.success("导入成功");
      emits("reloadTable");
      handleClose();
    });
  }
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
