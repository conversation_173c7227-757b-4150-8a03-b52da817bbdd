import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from '@vitejs/plugin-vue-jsx';
import path from "path";
import qiankun from "vite-plugin-qiankun";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";

const useDevMode = true;

export default ({ mode }) => {
  const __DEV__ = mode === "development";

  return defineConfig({
    plugins: [
      vue(),
      vueJsx(),
      qiankun("skybox-base", {
        useDevMode: useDevMode,
      }),
      createSvgIconsPlugin({
        // 多项目使用会影响，所以每个项目应唯一
        customDomId: "xinghuo-search-svgs",
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(process.cwd(), "src/assets/svgs")],
        // 指定symbolId格式
        symbolId: "[name]",
      }),
    ],
    base: __DEV__ ? "./" : "/lynxiao/skybox-base",
    server: {
      host: '0.0.0.0',
      port: 8081,
      proxy: {
        // 本地mock数据地址代理
        '/lynxiao/proxyApi/mock/': {
          target: 'http://localhost:8081',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/lynxiao\/proxyApi/, '')
        },
        // 后台接口代理
        "/lynxiao/proxyApi/local/": {
          target: "http://************:8080/",
          changeOrigin: true,
          secure: false ,
          rewrite: (path) => path.replace(/^\/lynxiao\/proxyApi\/local/, 'lynxiao/proxyApi')

        },
        // 后台接口代理
        "/lynxiao/proxyApi/lynxiao-support/": {
          // target: "http://**************:30111",
          target: "http://***********:8095",
          rewrite: (path) => path.replace(/^\/lynxiao\/proxyApi\/lynxiao-support/, ''),
          changeOrigin: true,
          secure: false ,
        },
        // 后台接口代理
        "/lynxiao/proxyApi/": {
          // target: "http://***********:30100",
          // target: "http://**************:30009/",//开发环境
          target: "http://127.0.0.1:30100/",
          // target: "http://**********:30100/", 
          changeOrigin: true,
          secure: false 
        },

      },
    },
    build: {
      target: "modules",
      outDir: "skybox-base",
      assetsDir: "assets",
      sourcemap: false,
      minify: "esbuild",
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        manualChunks(id) {
          if (id.includes("node_modules")) {
            return "vendor";
          }
        }
      },
    },
    resolve: {
      alias: [
        {
          find: "@",
          replacement: path.resolve(__dirname, "src"),
        },
      ],
    },
    esbuild: {
      pure: !__DEV__ ? ["console.log", "debugger"] : [],
    },
    css: {
      // 添加css预处理器配置
      preprocessorOptions: {
        scss: {
          // additionalData的内容会在每个scss文件的开头自动注入，这样就可以全局使用scss了
          additionalData: '@use "@/styles/vars.scss" as *; @use "@/styles/mixin.scss" as *; @use "@/styles/theme.scss" as *;'
        }
      }
    }
  });
};
